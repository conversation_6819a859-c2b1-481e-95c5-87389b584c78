server {
    listen       80;  # 监听 80 端口
    server_name  localhost;

    # 根目录指向静态文件
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri /index.html;  # 支持前端路由（如 React Router 或 Vue Router）
    }

    # API 转发规则
    location /api {
        # proxy_pass http://*************:8081/;
        proxy_pass http://localhost:8081/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_hide_header X-Powered-By;
        proxy_buffering on;
    }

    # 错误页面配置（可选）
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
