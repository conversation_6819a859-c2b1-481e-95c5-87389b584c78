<template>
  <div class="reset-password-page bg-grey-lighten-4">
    <v-container fluid class="fill-height">
      <v-row align="center" justify="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card class="elevation-4 rounded-lg reset-password-card animate-card">
            <!-- Logo区域 -->
            <div class="text-center pt-6">
              <v-avatar
                color="primary"
                size="64"
                class="mb-3"
              >
                <span class="text-h5 font-weight-bold white--text">M</span>
              </v-avatar>
              <h1 class="text-h4 font-weight-bold mb-1">Reset Password</h1>
              <p class="text-subtitle-1 text-medium-emphasis">
                Please enter your new password
              </p>
            </div>

            <v-card-text class="pt-4">
              <v-form @submit.prevent="resetPassword" v-model="isValid" ref="form">
                <v-text-field
                  v-model="newPassword"
                  label="New Password"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showPassword = !showPassword"
                  :type="showPassword ? 'text' : 'password'"
                  variant="outlined"
                  :rules="passwordRules"
                  required
                  class="mb-4"
                  density="comfortable"
                  bg-color="grey-lighten-5"
                  hint="Password must be at least 6 characters"
                  persistent-hint
                ></v-text-field>

                <v-text-field
                  v-model="confirmPassword"
                  label="Confirm New Password"
                  prepend-inner-icon="mdi-lock-check"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  variant="outlined"
                  :rules="confirmPasswordRules"
                  required
                  class="mb-4"
                  density="comfortable"
                  bg-color="grey-lighten-5"
                ></v-text-field>

                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ error }}
                </v-alert>

                <v-alert
                  v-if="success"
                  type="success"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ success }}
                </v-alert>

                <v-btn
                  type="submit"
                  color="primary"
                  block
                  size="large"
                  :loading="loading"
                  :disabled="!isValid || loading"
                  class="mb-4 reset-btn"
                  elevation="2"
                  height="48"
                >
                  Reset Password
                  <template v-slot:loader>
                    <v-progress-circular
                      indeterminate
                      color="white"
                      size="20"
                    ></v-progress-circular>
                  </template>
                </v-btn>

                <div class="text-center">
                  <p class="text-body-2 text-medium-emphasis">
                    <router-link to="/login" class="text-primary text-decoration-none">
                      Back to Login
                    </router-link>
                  </p>
                </div>
              </v-form>
            </v-card-text>

            <v-card-text class="text-center pb-6 text-medium-emphasis">
              <p class="text-caption">
                MCMD@TCMP2
              </p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ResetPassword',

  data() {
    return {
      newPassword: '',
      confirmPassword: '',
      error: '',
      success: '',
      loading: false,
      isValid: false,
      showPassword: false,
      showConfirmPassword: false,
      token: ''
    }
  },

  computed: {
    passwordRules() {
      return [
        v => !!v || 'Please enter password',
        v => (v && v.length >= 6) || 'Password must be at least 6 characters'
      ]
    },
    confirmPasswordRules() {
      return [
        v => !!v || 'Please confirm password',
        v => v === this.newPassword || 'Passwords do not match'
      ]
    }
  },

  mounted() {
    // Get reset token from URL parameters
    this.token = this.$route.query.token
    if (!this.token) {
      this.error = 'Invalid reset link, please request password reset again'
    }
  },

  methods: {
    async resetPassword() {
      if (!this.isValid || !this.token) return

      this.loading = true
      this.error = ''
      this.success = ''

      try {
        const response = await axios.post('/auth/reset-password', {
          token: this.token,
          newPassword: this.newPassword
        })

        if (response.data.success) {
          this.success = 'Password reset successful! Redirecting to login page in 3 seconds...'
          setTimeout(() => {
            this.$router.push('/login')
          }, 3000)
        } else {
          this.error = response.data.message || 'Reset failed, please try again later'
        }
      } catch (error) {
        console.error('Password reset failed:', error)
        this.error = error.response?.data?.message || 'Reset failed, please check if the reset link is valid'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.reset-password-page {
  min-height: calc(100vh - 64px);
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding-top: 80px;
}

.reset-password-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 16px !important;
  overflow: hidden;
  border: none !important;
}

.reset-password-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.reset-btn {
  letter-spacing: 0.5px;
}

/* 卡片进入动画 */
.animate-card {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框样式 */
:deep(.v-field) {
  border-radius: 8px !important;
}

:deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* 链接样式 */
.text-primary {
  font-weight: 500;
  transition: color 0.2s ease;
}

.text-primary:hover {
  color: rgb(var(--v-theme-primary)) !important;
  text-decoration: underline !important;
}
</style>
