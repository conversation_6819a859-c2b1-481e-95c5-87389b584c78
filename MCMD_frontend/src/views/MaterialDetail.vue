<template>
  <v-container fluid class="material-detail">
    <!-- Loading and Error States -->
    <loading-overlay :loading="loading && !partialLoaded" />
    <error-alert :message="error" @update:message="error = $event" />

    <!-- No Data State -->
    <v-row v-if="!loading && !material">
      <v-col cols="12" class="text-center">
        <v-icon size="64" color="grey">mdi-alert</v-icon>
        <h2 class="text-h5 grey--text">No Data Available</h2>
      </v-col>
    </v-row>

    <!-- Material Content -->
    <template v-if="!loading && material || partialLoaded">
      <!-- Header with Back Button and Title -->
      <v-row>
        <v-col cols="12">
          <div class="d-flex align-center mb-6">
            <v-btn
              color="primary"
              @click="$router.push('/')"
              variant="outlined"
              class="text-uppercase"
            >
              <v-icon start>mdi-arrow-left</v-icon>
              Back
            </v-btn>
            <h1
              class="text-h3 font-weight-bold text-center flex-grow-1"
              v-html="safeFormatFormula(material.Formula)"
            ></h1>
            <v-btn
              color="secondary"
              @click="handleAIAnalysisClick"
              variant="outlined"
              class="text-uppercase"
              :loading="aiAnalyzing && showAIDialog"
              style="min-width: 120px;"
            >
              <v-icon start>mdi-brain</v-icon>
              {{ getAIButtonText() }}
            </v-btn>
          </div>
        </v-col>
      </v-row>

      <!-- Main Content Grid -->
      <v-row>
        <!-- 3D Structure and Unit Cell Parameters Row -->
        <v-col cols="12" class="mb-6">
          <v-row>
            <!-- 3D Structure -->
            <v-col cols="12" md="6">
              <v-card class="rounded-lg crystal-structure-card">
                <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
                  <v-icon color="white" class="mr-2">mdi-atom</v-icon>
                  <span class="text-h6 font-weight-bold white--text"
                    >Crystal Structure</span
                  >
                </v-card-title>
                <v-card-text class="pa-6 structure-container">
                  <div v-if="cifLoading" class="d-flex justify-center align-center" style="height: 400px">
                    <div class="text-center">
                      <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
                      <div class="mt-4 text-body-1">Loading 3D structure...</div>
                    </div>
                  </div>
                  <ChemDoodle v-else :cifs="cifs" :key="componentKey" />
                </v-card-text>
              </v-card>
            </v-col>

            <!-- Unit Cell Parameters和Symmetry Information放在一起 -->
            <v-col cols="12" md="6">
              <div class="d-flex flex-column h-100">
                <!-- 材料ID卡片 - 只有表头 -->
                <v-card class="rounded-lg mb-4 flex-grow-0">
                  <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
                    <span class="text-h6 font-weight-bold white--text">ID: {{ material.id || $route.params.id }}</span>
                  </v-card-title>
                </v-card>
                
                <!-- Unit Cell Parameters -->
                <v-card class="rounded-lg mb-4 flex-grow-0 table-card">
                  <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
                    <v-icon color="white" class="mr-2">mdi-cube-outline</v-icon>
                    <span class="text-h6 font-weight-bold white--text"
                      >Unit Cell Parameters</span
                    >
                  </v-card-title>
                  <v-card-text class="pa-0">
                    <v-table class="custom-table">
                      <tbody>
                        <tr>
                          <td class="param-label">a (Å)</td>
                          <td class="param-value">{{ formatNumber(material.a) }}</td>
                          <td class="param-label">α (°)</td>
                          <td class="param-value">{{ formatNumber(material.a1) }}</td>
                        </tr>
                        <tr>
                          <td class="param-label">b (Å)</td>
                          <td class="param-value">{{ formatNumber(material.b) }}</td>
                          <td class="param-label">β (°)</td>
                          <td class="param-value">{{ formatNumber(material.b1) }}</td>
                        </tr>
                        <tr>
                          <td class="param-label">c (Å)</td>
                          <td class="param-value">{{ formatNumber(material.c) }}</td>
                          <td class="param-label">γ (°)</td>
                          <td class="param-value">{{ formatNumber(material.c1) }}</td>
                        </tr>
                      </tbody>
                    </v-table>
                  </v-card-text>
                </v-card>

                <!-- Symmetry Information -->
                <v-card class="rounded-lg flex-grow-1 table-card">
                  <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
                    <v-icon color="white" class="mr-2">mdi-hexagon-multiple</v-icon>
                    <span class="text-h6 font-weight-bold white--text"
                      >Symmetry Information</span
                    >
                  </v-card-title>
                  <v-card-text class="pa-0">
                    <v-table class="custom-table">
                      <tbody>
                        <tr>
                          <td class="param-label">Space Group Number</td>
                          <td class="param-value">{{ material.Space_group_num }}</td>
                        </tr>
                        <tr>
                          <td class="param-label">Space Group</td>
                          <td class="param-value" v-html="safeFormatSpaceGroup(material.Space_group_sym)"></td>
                        </tr>
                        <tr>
                          <td class="param-label">Point Group</td>
                          <td class="param-value" v-html="safeFormatSpaceGroup(material.Point_group)"></td>
                        </tr>
                        <tr>
                          <td class="param-label">Crystal System</td>
                          <td class="param-value">{{ material.Crystal_system }}</td>
                        </tr>
                        <tr>
                          <td class="param-label">Magnetic Lattice</td>
                          <td class="param-value">{{ material.magnetic_lattice }}</td>
                        </tr>
                      </tbody>
                    </v-table>
                  </v-card-text>
                </v-card>
              </div>
            </v-col>
          </v-row>
        </v-col>

        <!-- XRD Pattern 整行 -->
        <v-col cols="12" class="mb-6">
          <div v-if="xrdLoading">
            <v-card class="rounded-lg">
              <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
                <v-icon color="white" class="mr-2">mdi-chart-bell-curve</v-icon>
                <span class="text-h6 font-weight-bold white--text">X-Ray Diffraction Pattern</span>
              </v-card-title>
              <v-card-text class="pa-6">
                <div class="d-flex justify-center align-center" style="height: 400px">
                  <div class="text-center">
                    <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
                    <div class="mt-4 text-body-1">Loading XRD pattern...</div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </div>
          <XRDChart v-else :xrd-data="xrdData" @retry-load-xrd="handleRetryXRD" />
        </v-col>

        <!-- Basic Properties和Magnetic Properties并排 -->
        <!-- Left Column - Basic Properties -->
        <v-col cols="12" md="6">
          <!-- Basic Properties -->
          <v-card class="rounded-lg table-card">
            <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
              <v-icon color="white" class="mr-2">mdi-chart-box-outline</v-icon>
              <span class="text-h6 font-weight-bold white--text"
                >Basic Properties</span
              >
            </v-card-title>
            <v-card-text class="pa-0">
              <v-table class="custom-table">
                <tbody>
                  <tr>
                    <td class="param-label-full">Density (g/cm³)</td>
                    <td class="param-value-full" v-html="safeFormatWithUnit(material.Density)"></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">V<sub>unit cell</sub> (nm³)</td>
                    <td class="param-value-full" v-html="safeFormatWithUnit(material.V_unit_cell)"></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Mole Mass (g/mol)</td>
                    <td class="param-value-full" v-html="safeFormatWithUnit(material.Mole_Mass)"></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Mole Volume (cm³/mol)</td>
                    <td class="param-value-full" v-html="safeFormatWithUnit(material.Mole_Volume)"></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">GGA band gap (eV)</td>
                    <td class="param-value-full">{{ material.GGA_band_gap || 'N/A' }}</td>
                  </tr>
                  <tr>
                    <td class="param-label-full">E<sub>hull</sub> (eV/atom)</td>
                    <td class="param-value-full">{{ material.E_hull || 'N/A' }}</td>
                  </tr>
                </tbody>
              </v-table>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Right Column - Magnetic Properties -->
        <v-col cols="12" md="6">
          <!-- Magnetic Properties -->
          <v-card class="rounded-lg table-card">
            <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
              <v-icon color="white" class="mr-2">mdi-magnet</v-icon>
              <span class="text-h6 font-weight-bold white--text"
                >Magnetic Properties</span
              >
            </v-card-title>
            <v-card-text class="pa-0">
              <v-table class="custom-table">
                <tbody>
                  <tr>
                    <td class="param-label-full">Mag. ion</td>
                    <td class="param-value-full" v-html="safeFormatIon(material.Mag_ion)"></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Mag. ion/V (nm⁻³)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.Mag_ion_V)"
                    ></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Mag. ion/m (g⁻³)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.Mag_ion_m)"
                    ></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Magnetic moment (μ<sub>B</sub>)</td>
                    <td class="param-value-full">{{ material.Magnetic_moment || 'N/A' }}</td>
                  </tr>
                  <tr>
                    <td class="param-label-full">Effective Spin (J)</td>
                    <td class="param-value-full">{{ material.Effective_Spin || 'N/A' }}</td>
                  </tr>
                  <tr>
                    <td class="param-label-full">S<sub>GS</sub>(Vol)=S<sub>GS</sub>(Mass)*ρ (mJ/K⋅cm³)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.S_GS_Vol)"
                    ></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">S<sub>GS</sub>(Mass)=S<sub>GS</sub>(mol)/M (mJ/K⋅g)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.S_GS_Mass)"
                    ></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">T<sub>N</sub>* (mK)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.T_N)"
                    ></td>
                  </tr>
                  <tr>
                    <td class="param-label-full">T<sub>access</sub> (mK)</td>
                    <td
                      class="param-value-full"
                      v-html="safeFormatWithUnit(material.T_access)"
                    ></td>
                  </tr>
                </tbody>
              </v-table>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Custom Properties 整行显示 -->
        <v-col cols="12" class="mt-6" v-if="hasCustomProperties">
          <v-card class="rounded-lg table-card">
            <v-card-title class="d-flex align-center py-4 px-6 bg-info">
              <v-icon color="white" class="mr-2">mdi-plus-circle-outline</v-icon>
              <span class="text-h6 font-weight-bold white--text">Custom Properties</span>
            </v-card-title>
            <v-card-text class="pa-0">
              <v-table class="custom-table">
                <tbody>
                  <tr v-for="(property, key) in customProperties" :key="key">
                    <td class="param-label">
                      {{ key }}
                      <span v-if="property.unit" class="text-caption text-grey">
                        ({{ property.unit }})
                      </span>
                    </td>
                    <td class="param-value">
                      <span v-if="property.type === 'boolean'">
                        <v-chip
                          :color="property.value ? 'success' : 'error'"
                          size="small"
                          variant="outlined"
                        >
                          {{ property.value ? '是' : '否' }}
                        </v-chip>
                      </span>
                      <span v-else>
                        {{ formatCustomPropertyValue(property) }}
                      </span>
                      <div v-if="property.description" class="text-caption text-grey mt-1">
                        {{ property.description }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Associated Publications 和 Data Contributor 并排 -->
        <v-col cols="12" md="6" class="mt-6">
          <!-- Associated Publications -->
          <v-card class="rounded-lg table-card">
            <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
              <v-icon color="white" class="mr-2"
                >mdi-book-open-page-variant</v-icon
              >
              <span class="text-h6 font-weight-bold white--text"
                >Associated Publications</span
              >
            </v-card-title>
            <v-card-text class="pa-0">
              <v-table class="custom-table">
                <tbody>
                  <tr>
                    <td class="param-label">DOI</td>
                    <td class="param-value">
                      <a
                        :href="formatDoiUrl(material.DOI)"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-primary text-decoration-none"
                        v-if="material.DOI"
                      >
                        {{ material.DOI }}
                      </a>
                      <span v-else class="text-grey">N/A</span>
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Data Contributor -->
        <v-col cols="12" md="6" class="mt-6">
          <v-card class="rounded-lg table-card">
            <v-card-title class="d-flex align-center py-4 px-6 bg-success">
              <v-icon color="white" class="mr-2">mdi-account-circle</v-icon>
              <span class="text-h6 font-weight-bold white--text"
                >Data Contributor</span
              >
            </v-card-title>
            <v-card-text class="pa-0">
              <v-table class="custom-table">
                <tbody>
                  <tr>
                    <td class="param-label">Contributor</td>
                    <td class="param-value">
                      <v-chip
                        color="success"
                        variant="outlined"
                        size="small"
                        prepend-icon="mdi-account"
                      >
                        {{ material.createdBy || 'Unknown' }}
                      </v-chip>
                    </td>
                  </tr>
                  <tr>
                    <td class="param-label">Created At</td>
                    <td class="param-value">
                      {{ formatDate(material.createdAt) }}
                    </td>
                  </tr>
                  <tr v-if="material.lastModifiedBy && material.lastModifiedBy !== material.createdBy">
                    <td class="param-label">Last Modified By</td>
                    <td class="param-value">
                      <v-chip
                        color="warning"
                        variant="outlined"
                        size="small"
                        prepend-icon="mdi-account-edit"
                      >
                        {{ material.lastModifiedBy }}
                      </v-chip>
                    </td>
                  </tr>
                  <tr v-if="material.lastModifiedAt && material.lastModifiedAt !== material.createdAt">
                    <td class="param-label">Last Modified At</td>
                    <td class="param-value">
                      {{ formatDate(material.lastModifiedAt) }}
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

    <!-- AI分析浮动窗口 -->
    <div
      v-if="showAIDialog"
      class="ai-floating-window"
      :class="{ 'dragging': isDragging }"
      :style="{
        left: floatingWindowPosition.x + 'px',
        top: floatingWindowPosition.y + 'px',
        width: floatingWindowSize.width + 'px',
        height: floatingWindowSize.height + 'px'
      }"
    >
      <v-card class="ai-analysis-card h-100 d-flex flex-column">
        <!-- 标题栏 - 可拖拽区域 -->
        <div
          class="d-flex align-center py-3 px-4 window-title-bar"
          style="
            cursor: move;
            flex-shrink: 0;
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
            color: white !important;
            min-height: 60px !important;
            border-top-left-radius: 12px !important;
            border-top-right-radius: 12px !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
            width: 100% !important;
            position: relative !important;
            z-index: 10 !important;
            user-select: none;
          "
          @mousedown="startDrag"
        >
          <v-icon style="color: white !important;" class="mr-2" size="20">mdi-brain</v-icon>
          <span class="text-subtitle-1 font-weight-bold" style="color: white !important;">AI Material Analysis</span>
          <div style="flex-grow: 1;"></div>

          <!-- 窗口控制按钮 -->
          <v-btn
            @click="toggleWindowSize"
            icon
            variant="text"
            size="x-small"
            class="mr-1"
            style="color: white !important;"
          >
            <v-icon size="16" style="color: white !important;">{{ isWindowMaximized ? 'mdi-window-restore' : 'mdi-window-maximize' }}</v-icon>
          </v-btn>
          <v-btn
            @click="minimizeWindow"
            icon
            variant="text"
            size="x-small"
            class="mr-1"
            style="color: white !important;"
          >
            <v-icon size="16" style="color: white !important;">mdi-window-minimize</v-icon>
          </v-btn>
          <v-btn
            @click="closeAIDialog"
            icon
            variant="text"
            size="x-small"
            style="color: white !important;"
          >
            <v-icon size="16" style="color: white !important;">mdi-close</v-icon>
          </v-btn>
        </div>

        <!-- 内容区域 -->
        <v-card-text class="pa-0 flex-grow-1 d-flex flex-column" style="overflow: hidden; min-height: 0;">
          <!-- 分析进行中 -->
          <div v-if="aiAnalyzing && !displayedText" class="loading-container">
            <div class="loading-content">
              <v-progress-circular
                indeterminate
                color="secondary"
                size="48"
                class="mb-3"
              ></v-progress-circular>
              <div class="text-subtitle-1 mb-1">AI is analyzing material data...</div>
              <div class="text-caption text-grey">{{ aiAnalysisStatus }}</div>
            </div>
          </div>



          <!-- 分析结果 -->
          <div v-if="displayedText || pendingText" class="ai-analysis-content flex-grow-1">
            <!-- 实时Markdown格式化显示 -->
            <div class="analysis-text custom-padding" :class="{ 'analysis-completed': !aiAnalyzing && !isTyping }">
              <div v-html="formattedHTML"></div>

            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="aiAnalysisError" class="flex-grow-1 d-flex flex-column justify-center align-center text-center py-8">
            <v-icon size="48" color="error" class="mb-3">mdi-alert-circle</v-icon>
            <div class="text-subtitle-1 mb-1 text-error">Analysis Failed</div>
            <div class="text-caption mb-3">{{ aiAnalysisError }}</div>
            <v-btn
              @click="retryAIAnalysis"
              color="secondary"
              variant="outlined"
              size="small"
            >
              <v-icon start size="16">mdi-refresh</v-icon>
              Retry
            </v-btn>
          </div>
        </v-card-text>

        <!-- 底部操作栏 - 始终存在，避免布局变化 -->
        <v-card-actions class="px-4 py-2 bg-grey-lighten-5" style="flex-shrink: 0; min-height: 56px;">
          <v-spacer></v-spacer>
          <!-- 只有在分析完成时才显示按钮 -->
          <template v-if="displayedText && !aiAnalyzing">
            <v-btn
              @click="clearAIAnalysis"
              color="grey"
              variant="outlined"
              size="small"
              class="mr-2"
            >
              <v-icon start size="16">mdi-refresh</v-icon>
              Re-analyze
            </v-btn>
            <v-btn
              @click="closeAIDialog"
              color="secondary"
              variant="elevated"
              size="small"
            >
              <v-icon start size="16">mdi-check</v-icon>
              Done
            </v-btn>
          </template>
        </v-card-actions>
      </v-card>
    </div>


    </template>
  </v-container>
</template>

<script>
import LoadingOverlay from "@/components/LoadingOverlay.vue";
import ErrorAlert from "@/components/ErrorAlert.vue";
import ChemDoodle from "@/components/ChemDoodle.vue";
import XRDChart from "@/components/XRDChart.vue";

export default {
  name: "MaterialDetail",

  components: {
    LoadingOverlay,
    ErrorAlert,
    ChemDoodle,
    XRDChart
  },

  data() {
    return {
      material: null,
      loading: false,
      partialLoaded: false,
      cifLoading: false,
      xrdLoading: false,
      error: null,

      // 请求控制器，用于取消进行中的请求
      currentRequestController: null,
      requestSequence: 0, // 请求序列号，用于识别最新请求
      cifs: {
        primitive: null,
        conventional: null,
      },
      xrdData: {
        CuKa: [],
        CuKa1: [],
        CuKa2: [],
      },
      componentKey: 0,
      // AI分析相关
      showAIDialog: false,
      aiAnalyzing: false,
      aiAnalysisResult: '',
      aiAnalysisError: null,
      aiAnalysisStatus: 'Connecting to AI service...',
      aiEventSource: null,
      // 打字机效果相关（参考demo实现）
      generatedText: '',
      displayedText: '',
      pendingText: '',
      formattedHTML: '', // 新增：存储实时格式化的HTML
      isTyping: false,
      typingInterval: null,
      // 浮动窗口相关
      floatingWindowPosition: { x: 100, y: 100 },
      floatingWindowSize: { width: 500, height: 400 },
      isWindowMaximized: false,
      isWindowMinimized: false,
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      originalSize: { width: 500, height: 400 },
      originalPosition: { x: 100, y: 100 },
      // 定时器管理
      streamingTimer: null,
      timeoutTimer: null,
      typingTimer: null
    };
  },

  computed: {
    // 检查是否有自定义属性
    hasCustomProperties() {
      return this.customProperties && Object.keys(this.customProperties).length > 0;
    },

    // 获取自定义属性对象
    customProperties() {
      if (!this.material || !this.material.customProperties) {
        return {};
      }

      // 如果customProperties是对象格式，直接返回
      if (typeof this.material.customProperties === 'object' && !Array.isArray(this.material.customProperties)) {
        return this.material.customProperties;
      }

      // 如果是其他格式，返回空对象
      return {};
    }
  },

  methods: {
    async fetchMaterial() {
      // 取消之前的请求
      if (this.currentRequestController) {
        this.currentRequestController.abort();
      }

      // 创建新的请求控制器
      this.currentRequestController = new AbortController();
      const currentSequence = ++this.requestSequence;

      try {
        this.loading = true;
        this.componentKey += 1;

        // 获取基本材料数据
        const data = await this.$http.get(
          `/materials/${this.$route.params.id}`,
          { signal: this.currentRequestController.signal }
        );

        // 检查是否是最新请求
        if (currentSequence !== this.requestSequence) {
          console.log('请求已过期，忽略响应');
          return;
        }

        this.material = data;

        // 标记基本数据已加载
        this.partialLoaded = true;

        // 降低全局loading状态
        this.loading = false;

        // 如果有材料ID，异步加载CIF和XRD数据
        if (data.id) {
          // 使用Promise.allSettled并行加载，确保错误处理同步
          try {
            const results = await Promise.allSettled([
              this.fetchCifUrl(data.id, currentSequence),
              this.fetchXrdData(data.id, currentSequence)
            ]);

            // 再次检查请求序列号
            if (currentSequence !== this.requestSequence) {
              console.log('CIF/XRD请求已过期，忽略响应');
              return;
            }

            // 检查加载结果，记录任何失败的操作
            results.forEach((result, index) => {
              if (result.status === 'rejected') {
                const operation = index === 0 ? 'CIF' : 'XRD';
                console.warn(`${operation} 数据加载失败:`, result.reason);
              }
            });
          } catch (error) {
            console.error('异步数据加载过程中发生错误:', error);
          }
        }
      } catch (error) {
        // 如果是请求被取消，不显示错误
        if (error.name === 'AbortError') {
          console.log('请求被取消');
          return;
        }

        // 检查是否是最新请求
        if (currentSequence !== this.requestSequence) {
          return;
        }

        this.error = "Failed to load material data, please ensure you are logged in and have permission to access this resource";
        this.loading = false;
      } finally {
        // 清理请求控制器
        if (this.currentRequestController && currentSequence === this.requestSequence) {
          this.currentRequestController = null;
        }
      }
    },
    
    // 从后端API获取CIF文件内容
    async fetchCifUrl(materialId, requestSequence = null) {
      try {
        this.cifLoading = true;

        // 调用我们的后端接口获取CIF文件内容
        const response = await this.$http.get(`/${materialId}/cif`);

        // 检查请求序列号（如果提供）
        if (requestSequence && requestSequence !== this.requestSequence) {
          console.log('CIF请求已过期，忽略响应');
          return;
        }

        if (response) {
          // 将获取到的CIF文件内容设置到cifs对象中
          this.cifs = {
            primitive: response,
            conventional: response, // 如果没有区分，可以设置为相同的内容
          };
          this.componentKey += 1; // 强制重新渲染ChemDoodle组件
        }
      } catch (error) {
        console.error('CIF文件加载失败:', error);
        this.cifLoading = false;
        // 抛出错误以便Promise.allSettled能够捕获
        throw new Error('CIF文件加载失败: ' + (error.message || '未知错误'));
      } finally {
        this.cifLoading = false;
      }
    },
    
    // 从后端API获取XRD数据
    async fetchXrdData(materialId, requestSequence = null) {
      try {
        this.xrdLoading = true;
        
        // 先获取材料的化学式
        if (!this.material || !this.material.Formula) {
          console.warn('XRD数据加载失败: 材料化学式信息缺失');
          this.xrdLoading = false;
          // 不设置全局错误，因为这不是致命错误
          return;
        }

        // 调用后端API获取XRD数据
        const response = await this.$http.get(`/${materialId}/xrd`, {
          responseType: 'blob' // 使用blob类型接收二进制数据
        });

        // 检查请求序列号（如果提供）
        if (requestSequence && requestSequence !== this.requestSequence) {
          console.log('XRD请求已过期，忽略响应');
          return;
        }

        if (response) {
          // 处理接收到的ZIP文件
          const zip = await this.processXrdZipFile(response);

          if (zip) {
            // 查找包含化学式的文件名
            const fileNames = Object.keys(zip.files);

            // 找到匹配的波长文件
            const file1_54056 = fileNames.find(name => name.endsWith("_1.54056.xy"));
            const file1_54184 = fileNames.find(name => name.endsWith("_1.54184.xy"));
            const file1_54439 = fileNames.find(name => name.endsWith("_1.54439.xy"));

            // 解析XRD数据并设置到xrdData对象
            this.xrdData = {
              CuKa1: await this.parseXrdFile(zip, file1_54056), // 波长1.54056Å对应CuKa1
              CuKa: await this.parseXrdFile(zip, file1_54184),  // 波长1.54184Å对应CuKa (平均)
              CuKa2: await this.parseXrdFile(zip, file1_54439)  // 波长1.54439Å对应CuKa2
            };
          }
        }
      } catch (error) {
        console.error('获取XRD数据失败:', error);
        this.xrdError = 'Failed to get XRD data, please try again later';
        this.xrdData = null;
        this.xrdLoading = false;
        // 抛出错误以便Promise.allSettled能够捕获
        throw new Error('XRD数据加载失败: ' + (error.message || '未知错误'));
      } finally {
        this.xrdLoading = false;
      }
    },

    // 处理XRD重试事件
    handleRetryXRD() {
      if (this.material && this.material.id) {
        // 重新获取XRD数据
        this.fetchXrdData(this.material.id);
      }
    },

    // 处理XRD ZIP文件
    async processXrdZipFile(blob) {
      try {
        // 使用JSZip库解压ZIP文件
        const JSZip = (await import('jszip')).default;
        const zip = new JSZip();

        // 加载ZIP内容
        const loadedZip = await zip.loadAsync(blob);

        // 检查ZIP文件内容
        const fileNames = Object.keys(loadedZip.files);

        if (fileNames.length === 0) {
          return null;
        }

        return loadedZip;
      } catch (error) {
        return null;
      }
    },
    
    // 从ZIP文件中解析XRD文件数据
    async parseXrdFile(zip, filename) {
      try {
        if (!zip || !filename || !zip.files[filename]) {
          return [];
        }

        // 从ZIP中提取文件内容
        const content = await zip.files[filename].async('text');

        // 解析XY文件内容（假设是以tab分隔的两列数据）
        const dataPoints = content.split('\n')
          .filter(line => line && line.trim() !== '') // 确保line存在
          .map(line => {
            const parts = line.split('\t');
            if (!parts || parts.length !== 2) {
              return null;
            }

            // 安全地获取数组元素，确保存在且不为空
            const xStr = parts[0];
            const yStr = parts[1];

            if (!xStr || !yStr || xStr.trim() === '' || yStr.trim() === '') {
              return null;
            }

            const x = parseFloat(xStr.trim());
            const y = parseFloat(yStr.trim());

            if (isNaN(x) || isNaN(y) || !isFinite(x) || !isFinite(y)) {
              return null;
            }

            // 返回[x,y]数组格式，而不是{x,y}对象，以匹配ECharts期望的格式
            return [x, y];
          })
          .filter(point => point !== null);

        return dataPoints;
      } catch (error) {
        return [];
      }
    },

    formatNumber(value) {
      if (value === null || value === undefined || isNaN(value)) {
        return "N/A";
      }
      return Number(value).toFixed(2);
    },

    // 安全的格式化函数 - 对输入进行严格验证后生成HTML
    safeFormatFormula(formula) {
      if (!formula) return 'N/A'

      // 严格验证输入，只允许字母、数字、常见符号
      const sanitized = formula.replace(/[^A-Za-z0-9\-+()[\]._\s]/g, '')

      // 只对数字添加下标，其他内容保持原样
      return sanitized.replace(/(\d+)/g, '<sub>$1</sub>')
    },

    // 安全的空间群格式化
    safeFormatSpaceGroup(spaceGroup) {
      if (!spaceGroup) return 'N/A'

      // 严格验证输入，只允许空间群相关字符
      const sanitized = spaceGroup.replace(/[^A-Za-z0-9\-+/_\s]/g, '')

      // 处理空间群格式：字母斜体，数字下标，负号上划线
      let formatted = sanitized
        .replace(/([A-Za-z])/g, '<i>$1</i>')  // 字母斜体
        .replace(/-(\d)/g, '<span style="text-decoration: overline;">$1</span>')  // 负号上划线
        .replace(/_(\d)/g, '<sub>$1</sub>')  // 下标
        .replace(/_/g, '')  // 移除多余的下划线

      return formatted
    },

    // 安全的离子格式化
    safeFormatIon(ion) {
      if (!ion) return 'N/A'

      // 严格验证输入，只允许离子相关字符
      const sanitized = ion.replace(/[^A-Za-z0-9\-+^.\s]/g, '')

      // 处理离子格式：上标电荷
      return sanitized.replace(/(\^)([-+\d]+)/g, '<sup>$2</sup>')
    },

    // 安全的单位格式化
    safeFormatWithUnit(value) {
      if (!value) return 'N/A'

      // 严格验证输入
      const sanitized = String(value).replace(/[^A-Za-z0-9\-+^./\s()]/g, '')

      // 处理上标，如 nm^3 -> nm³
      return sanitized.replace(/(\^)([-+\d]+)/g, '<sup>$2</sup>')
    },

    // 安全的格式化函数 - 返回纯文本，避免XSS风险
    formatFormula(formula) {
      if (!formula) return "N/A";
      // 返回纯文本，不包含HTML标签
      return formula;
    },

    formatSpaceGroup(spaceGroup) {
      if (!spaceGroup) return "N/A";
      // 按照SpaceGroupTable.vue的标准格式化
      // 处理字母为斜体
      let formatted = spaceGroup.replace(/([A-Za-z])/g, '<i>$1</i>');
      // 先处理带横线的文本（反演轴用上划线）
      formatted = formatted.replace(/-(\d)/g, '<span style="text-decoration: overline;">$1</span>');
      // 然后处理下标文本（只处理下划线后的数字）
      formatted = formatted.replace(/_(\d)/g, '<sub>$1</sub>')
        .replace(/_/g, '');
      return formatted;
    },

    formatPointGroup(pointGroup) {
      if (!pointGroup) return "N/A";
      // 按照SpaceGroupTable.vue的标准格式化Point Group
      // 处理字母为斜体
      let formatted = pointGroup.replace(/([A-Za-z])/g, '<i>$1</i>');
      // 先处理带横线的文本（反演轴用上划线）
      formatted = formatted.replace(/-(\d)/g, '<span style="text-decoration: overline;">$1</span>');
      // 然后处理下标文本（只处理下划线后的数字）
      formatted = formatted.replace(/_(\d)/g, '<sub>$1</sub>')
        .replace(/_/g, '');
      return formatted;
    },

    formatIon(ion) {
      if (!ion) return "N/A";
      return ion.replace(/(\^)([-+\d]+)/g, "<sup>$2</sup>");
    },

    formatWithUnit(value) {
      if (!value) return "N/A";
      // 处理上标，如 nm^3 -> nm<sup>3</sup>
      return value.replace(/(\^)([-+\d]+)/g, "<sup>$2</sup>");
    },

    formatDate(dateString) {
      if (!dateString) {
        return 'N/A';
      }

      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return 'N/A';
        }

        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      } catch (e) {
        return 'N/A';
      }
    },

    // 格式化自定义属性值
    formatCustomPropertyValue(property) {
      if (!property || property.value === null || property.value === undefined) {
        return 'N/A';
      }

      const value = property.value;

      // 根据类型格式化值
      switch (property.type) {
        case 'number':
          return typeof value === 'number' ? value.toString() : value;
        case 'boolean':
          return value ? '是' : '否';
        case 'string':
        default:
          return value.toString();
      }
    },

    // 格式化DOI为可访问的URL
    formatDoiUrl(doi) {
      if (!doi) return '#';

      // 如果DOI已经是完整的URL，直接返回
      if (doi.startsWith('http://') || doi.startsWith('https://')) {
        return doi;
      }

      // 如果DOI以"doi:"开头，去掉前缀
      const cleanDoi = doi.replace(/^doi:\s*/i, '');

      // 返回标准的DOI URL
      return `https://doi.org/${cleanDoi}`;
    },

    // 处理AI分析按钮点击
    handleAIAnalysisClick() {
      if (!this.showAIDialog && (this.aiAnalyzing || this.displayedText)) {
        // 如果窗口被最小化但分析正在进行或已完成，则恢复窗口
        this.showAIDialog = true;
      } else {
        // 否则开始新的分析
        this.startAIAnalysis();
      }
    },

    // 获取AI按钮文本
    getAIButtonText() {
      if (!this.showAIDialog && (this.aiAnalyzing || this.displayedText)) {
        return this.aiAnalyzing ? 'AI Analyzing...' : 'View Analysis';
      }
      return 'AI Analysis';
    },

    // AI分析相关方法 - 参考demo的AIStream.vue实现
    async startAIAnalysis() {
      if (!this.material || !this.material.id) {
        this.aiAnalysisError = 'Material data is incomplete, unable to perform AI analysis';
        this.showAIDialog = true;
        return;
      }

      // 如果正在加载，则返回
      if (this.aiAnalyzing) return;

      // 检查EventSource支持
      if (!('EventSource' in window)) {
        this.aiAnalysisError = 'Browser does not support Server-Sent Events, please use a modern browser';
        this.showAIDialog = true;
        return;
      }

      // 初始化窗口位置并打开对话框
      this.initializeWindowPosition();
      this.showAIDialog = true;
      this.resetAIState();
      this.aiAnalyzing = true;
      this.aiAnalysisStatus = 'Connecting to AI service...';

      // 安全关闭之前的连接（如果存在）
      this.cleanupEventSource();

      // 创建EventSource连接
      const materialId = this.material.id;
      // 使用配置的API基础URL，支持开发和生产环境
      const baseUrl = process.env.NODE_ENV === 'production'
        ? window.location.origin
        : 'http://localhost:8081';
      const eventSourceUrl = `${baseUrl}/materials/${materialId}/analyze`;

      // 检查用户登录状态
      const username = sessionStorage.getItem('username');
      if (!username) {
        this.aiAnalysisError = '用户未登录，请先登录';
        this.resetStream();
        return;
      }

      // 检查Cookie中是否有JWT token（Cookie会自动发送）
      const hasToken = this.getJWTToken();
      if (!hasToken) {
        // 尝试刷新token
        try {
          await this.$http.post('/refresh-token');

          // 重新检查token
          if (!this.getJWTToken()) {
            this.aiAnalysisError = 'Authentication failed, please re-login and try again';
            this.resetStream();
            return;
          }
        } catch (refreshError) {
          this.aiAnalysisError = 'Authentication failed, please re-login and try again';
          this.resetStream();
          return;
        }
      }

      // 创建EventSource连接
      // 注意：由于前端(8080)和后端(8081)是跨域，EventSource不会自动发送Cookie
      // 因此我们仍需要使用URL参数方式传递token
      // TODO: 安全改进 - 考虑使用POST请求获取临时流式URL，避免token在URL中暴露

      const token = this.getJWTToken();
      if (token) {
        // 安全警告：Token在URL中传递存在安全风险（可能在服务器日志中暴露）
        // 建议后续版本改为使用临时授权URL或WebSocket连接
        const eventSourceUrlWithToken = `${eventSourceUrl}?token=${encodeURIComponent(token)}`;
        this.aiEventSource = new EventSource(eventSourceUrlWithToken);
      } else {
        this.aiAnalysisError = 'Authentication failed, please re-login and try again';
        this.resetStream();
        return;
      }

      // 启动流式传输监控
      this.checkStreamingProgress();

      // 连接打开
      this.aiEventSource.onopen = () => {
        this.aiAnalysisStatus = 'Connection established, analyzing material data...';
      };

      // 接收数据 - 关键：实现真正的流式效果
      this.aiEventSource.onmessage = (event) => {


        // 立即更新状态以显示收到数据
        this.aiAnalysisStatus = 'Receiving AI analysis data...';

        // 检查EventSource支持
        if (!('EventSource' in window)) {
          return;
        }

        // 检查数据是否为空
        if (event.data && event.data.trim() !== '') {
          try {
            // 尝试解析JSON（如果后端发送的是JSON格式）
            let textData;
            try {
              const jsonData = JSON.parse(event.data);
              textData = typeof jsonData === 'string' ? jsonData : (jsonData.text || JSON.stringify(jsonData));
            } catch (e) {
              // 不是JSON，当作纯文本处理
              textData = event.data;
            }

            // 后端发送完整内容，但我们需要实现增量显示以保持打字机效果
            const newContent = String(textData);

            // 检查是否包含空格推送信号（AI模型在末尾添加的空格）
            const hasTrailingSpaces = /\s{8,}$/.test(newContent); // 8个或以上末尾空格

            if (hasTrailingSpaces) {

              // 清理末尾空格，保留实际内容
              let finalContent = newContent.replace(/\s{8,}$/, '').trimEnd();

              // 立即显示所有内容
              this.displayedText = finalContent;
              this.pendingText = '';

              // 停止打字效果并完成流式传输
              this.stopTyping();
              this.updateFormattedHTML();
              this.finishStreaming();
              return;
            }

            // 检查是否有新内容
            if (newContent.length > this.displayedText.length) {
              // 有新内容，设置为待显示内容并启动打字机效果
              this.pendingText = newContent.substring(this.displayedText.length);

              // 如果当前没有在打字，启动打字机效果
              if (!this.isTyping) {
                this.startTyping();
              }
            } else if (newContent !== this.displayedText) {
              // 内容完全不同（可能是重新开始），直接替换
              this.displayedText = newContent;
              this.pendingText = '';
              this.updateFormattedHTML();
            }
            // 如果内容相同，不做任何处理，避免重新渲染

            // 自动滚动到底部
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          } catch (error) {
            // 处理消息解析错误，但不中断流式传输
            console.warn('AI analysis message parsing failed:', error.message);
          }
        }
      };

      // 连接正常关闭
      this.aiEventSource.onclose = () => {
        this.finishStreaming();
      };

      // 连接错误
      this.aiEventSource.onerror = () => {
        // 检查连接状态
        if (this.aiEventSource.readyState === EventSource.CLOSED) {
          // 连接已关闭，这通常是正常的流式传输完成
          this.finishStreaming();
        } else if (this.aiEventSource.readyState === EventSource.CONNECTING) {
          // 连接中状态出现错误，这是真正的连接问题
          this.aiAnalysisError = 'AI analysis connection failed, possibly an authentication issue, please re-login and try again';
          this.resetStream();
        } else {
          // 其他异常状态
          this.aiAnalysisError = 'AI analysis connection failed, please check network connection';
          this.resetStream();
        }
      };
    },

    // 开始执行逐字显示效果，参考demo实现
    startTyping() {
      if (this.isTyping || !this.pendingText) {
        return;
      }

      this.isTyping = true;
      this.processNextChar();
    },

    // 处理下一个字符的显示 - 优化速度并实时格式化Markdown
    processNextChar() {
      if (!this.pendingText || this.pendingText.length === 0) {
        this.isTyping = false;
        return;
      }

      // 自适应处理速度：根据待处理文本长度调整
      let chunkSize, delay;
      if (this.pendingText.length > 100) {
        // 大量文本时，快速处理
        chunkSize = Math.min(10, this.pendingText.length);
        delay = 10;
      } else if (this.pendingText.length > 20) {
        // 中等文本时，中等速度
        chunkSize = Math.min(5, this.pendingText.length);
        delay = 20;
      } else {
        // 少量文本时，慢速处理以保持打字机效果
        chunkSize = Math.min(2, this.pendingText.length);
        delay = 30;
      }

      // 安全地处理字符串操作，确保不为null
      const pendingText = this.pendingText || '';
      const displayedText = this.displayedText || '';

      const nextChunk = pendingText.substring(0, chunkSize);

      // 使用Vue响应式系统 - 创建新的字符串引用
      this.displayedText = String(displayedText + nextChunk);
      this.pendingText = String(pendingText.substring(chunkSize));

      // 关键改进：实时更新格式化的HTML
      this.updateFormattedHTML();

      // 自动滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 设置下一个字符块的延迟
      this.typingInterval = setTimeout(() => {
        this.processNextChar();
      }, delay);
    },

    // 新增方法：直接显示后端处理结果 - 完全信任后端
    updateFormattedHTML() {
      try {
        // 检查是否已经完成分析，如果是则不再重新格式化
        if (!this.aiAnalyzing && !this.isTyping && this.formattedHTML) {
          return;
        }

        // 完全信任后端处理结果，直接转换为HTML显示
        this.formattedHTML = this.directMarkdownToHTML(this.displayedText);

      } catch (error) {
        // 如果格式化失败，回退到纯文本
        this.formattedHTML = this.displayedText.replace(/\n/g, '<br>');
      }
    },

    // 停止打字效果
    stopTyping() {
      this.isTyping = false;
      if (this.typingInterval) {
        clearTimeout(this.typingInterval);
        this.typingInterval = null;
      }
    },

    // 自动滚动到底部
    scrollToBottom() {
      if (this.$refs.analysisTextContainer) {
        const container = this.$refs.analysisTextContainer;
        container.scrollTop = container.scrollHeight;
      }
    },

    // 完成流式传输
    finishStreaming() {
      this.aiAnalyzing = false;

      // 停止打字效果
      this.stopTyping();

      // 确保所有待显示的文本都显示出来，但不重新格式化
      if (this.pendingText) {
        // 直接完成剩余文本的显示，不使用打字机效果
        this.displayedText += this.pendingText;
        this.pendingText = '';

        // 关键：只有在有剩余内容时才更新HTML，且这是最后一次更新
        this.updateFormattedHTML();
      }

      // 完成后不重新格式化，保持当前样式稳定

      // 安全关闭EventSource连接
      if (this.aiEventSource) {
        // 先移除事件监听器，避免触发onerror
        this.aiEventSource.onmessage = null;
        this.aiEventSource.onerror = null;
        this.aiEventSource.onclose = null;
        this.aiEventSource.close();
        this.aiEventSource = null;
      }


    },

    // 重置流状态，参考demo实现
    resetStream() {
      this.aiAnalyzing = false;
      this.stopTyping();

      // 清理所有定时器
      if (this.timeoutTimer) {
        clearTimeout(this.timeoutTimer);
        this.timeoutTimer = null;
      }

      if (this.streamingTimer) {
        clearTimeout(this.streamingTimer);
        this.streamingTimer = null;
      }

      // 使用统一的EventSource清理方法
      this.cleanupEventSource();
      // 确保所有待显示的文本都显示出来
      if (this.pendingText) {
        this.displayedText += this.pendingText;
        this.pendingText = '';
      }
    },

    // 重置AI状态
    resetAIState() {
      this.generatedText = '';
      this.displayedText = '';
      this.pendingText = '';
      this.formattedHTML = ''; // 新增：重置格式化HTML
      this.aiAnalysisResult = '';
      this.aiAnalysisError = null;
      this.aiAnalysisStatus = '';
      this.stopTyping();
    },

    // 关闭AI对话框
    closeAIDialog() {
      this.showAIDialog = false;
      this.resetStream();
    },

    // 重试AI分析
    retryAIAnalysis() {
      this.resetAIState();
      this.startAIAnalysis();
    },

    // 清除分析结果并重新开始
    clearAIAnalysis() {
      this.resetAIState();
      this.startAIAnalysis();
    },

    // 浮动窗口控制方法
    startDrag(event) {
      // 检查是否点击了按钮，如果是则不拖拽
      if (event.target.closest('button') || event.target.closest('.v-btn')) {
        return;
      }

      this.isDragging = true;
      this.dragOffset.x = event.clientX - this.floatingWindowPosition.x;
      this.dragOffset.y = event.clientY - this.floatingWindowPosition.y;

      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
      event.preventDefault();
      event.stopPropagation();
    },

    onDrag(event) {
      if (!this.isDragging) return;

      this.floatingWindowPosition.x = event.clientX - this.dragOffset.x;
      this.floatingWindowPosition.y = event.clientY - this.dragOffset.y;

      // 限制窗口不能拖出屏幕
      const maxX = window.innerWidth - this.floatingWindowSize.width;
      const maxY = window.innerHeight - this.floatingWindowSize.height;

      this.floatingWindowPosition.x = Math.max(0, Math.min(maxX, this.floatingWindowPosition.x));
      this.floatingWindowPosition.y = Math.max(0, Math.min(maxY, this.floatingWindowPosition.y));
    },

    stopDrag() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    },

    toggleWindowSize() {
      if (this.isWindowMaximized) {
        // 恢复原始大小
        this.floatingWindowSize = { ...this.originalSize };
        this.floatingWindowPosition = { ...this.originalPosition };
        this.isWindowMaximized = false;
      } else {
        // 保存当前大小和位置
        this.originalSize = { ...this.floatingWindowSize };
        this.originalPosition = { ...this.floatingWindowPosition };

        // 最大化
        this.floatingWindowSize = {
          width: window.innerWidth - 80,
          height: window.innerHeight - 120
        };
        this.floatingWindowPosition = { x: 40, y: 80 };
        this.isWindowMaximized = true;
      }
    },

    minimizeWindow() {
      // 最小化：隐藏窗口但保持状态，不显示右下角按钮
      this.showAIDialog = false;
      this.isWindowMinimized = false; // 不设置为true，这样就不会显示右下角按钮
    },

    restoreWindow() {
      // 恢复窗口
      this.showAIDialog = true;
      this.isWindowMinimized = false;
    },

    // 初始化窗口位置
    initializeWindowPosition() {
      // 将窗口居中显示
      this.floatingWindowPosition.x = (window.innerWidth - this.floatingWindowSize.width) / 2;
      this.floatingWindowPosition.y = (window.innerHeight - this.floatingWindowSize.height) / 2;
    },

    // 检查流式传输进度，如果卡住则使用回退机制
    checkStreamingProgress() {
      // 清理之前的定时器
      if (this.streamingTimer) {
        clearTimeout(this.streamingTimer);
      }

      // 3秒后检查是否有数据流入
      this.streamingTimer = setTimeout(() => {
        if (this.aiAnalyzing && this.pendingText && !this.displayedText) {
          this.displayedText = this.pendingText;
          this.pendingText = '';
          this.isTyping = false;
        }
      }, 3000);

      // 10秒后检查是否完全没有响应
      this.timeoutTimer = setTimeout(() => {
        if (this.aiAnalyzing && !this.displayedText && !this.pendingText) {
          this.aiAnalysisError = 'Streaming timeout, please check network connection or retry';
          this.resetStream();
        }
      }, 10000);
    },

    // 统一的资源清理方法
    cleanupAllResources() {

      // 1. 清理AI分析相关
      this.stopTyping();
      this.cleanupEventSource();

      // 2. 清理所有定时器
      const timers = [
        'typingTimer', 'typingInterval', 'timeoutTimer',
        'streamingTimer', 'retryTimer', 'progressTimer'
      ];

      timers.forEach(timerName => {
        if (this[timerName]) {
          try {
            clearTimeout(this[timerName]);
            clearInterval(this[timerName]); // 同时清理interval类型
          } catch (error) {
            console.warn(`清理定时器 ${timerName} 时出错:`, error);
          } finally {
            this[timerName] = null;
          }
        }
      });

      // 3. 清理拖拽事件监听器
      try {
        this.cleanupDragListeners();
      } catch (error) {
        console.warn('清理拖拽监听器时出错:', error);
      }

      // 4. 重置状态
      this.aiAnalyzing = false;
      this.isTyping = false;
      this.pendingText = '';

      // 5. 清理HTTP请求（如果有AbortController）
      if (this.currentRequestController) {
        try {
          this.currentRequestController.abort();
          this.currentRequestController = null;
        } catch (error) {
          console.warn('取消HTTP请求时出错:', error);
        }
      }
    },

    // 公共方法：解析Cookie
    parseCookies() {
      try {
        if (!document.cookie) return {};

        const cookies = {};
        document.cookie.split(';').forEach(cookie => {
          const parts = cookie.trim().split('=');
          if (parts.length === 2) {
            const name = parts[0].trim();
            const value = parts[1].trim();
            cookies[name] = value;
          }
        });
        return cookies;
      } catch (error) {
        console.warn('解析Cookie失败:', error);
        return {};
      }
    },

    // 获取JWT Token的方法
    getJWTToken() {
      // 方法1：尝试从Cookie中读取
      try {
        const cookies = this.parseCookies();
        if (cookies.jwt_token) {
          return cookies.jwt_token;
        }
      } catch (error) {
        console.warn('获取JWT Token失败:', error.message);
      }

      // 方法2：尝试从sessionStorage中读取
      try {
        const token = sessionStorage.getItem('jwt_token');
        if (token) {
          return token;
        }
      } catch (error) {
        console.warn('从sessionStorage获取JWT Token失败:', error.message);
      }

      // 方法3：尝试从localStorage中读取
      try {
        const token = localStorage.getItem('jwt_token');
        if (token) {
          return token;
        }
      } catch (error) {
        console.warn('从localStorage获取JWT Token失败:', error.message);
      }

      return null;
    },

    // 专门检查Cookie中的JWT token
    getCookieToken() {
      try {
        const cookies = this.parseCookies();
        return cookies.jwt_token || null;
      } catch (error) {
        console.warn('读取Cookie失败:', error);
        return null;
      }
    },



    // 直接转换Markdown为HTML - 完全信任后端处理结果
    directMarkdownToHTML(text) {
      if (!text) return '';

      // 处理Markdown文本
      // 前端实时清理反引号和其他问题（重要！）
      text = text.replace(/^`+\s*/g, '');  // 清理开头的反引号
      text = text.replace(/^\s*`+\s*/g, ''); // 清理开头空格+反引号
      text = text.replace(/^[·….`]+\s*/g, ''); // 清理开头的省略号和反引号

      // 智能清理不完整的内容（前端额外保护）
      // 1. 清理结尾的单独标签（注：、备注：等）
      text = text.replace(/\n\s*(注|备注|说明|提示)：\s*$/g, '');
      text = text.replace(/^\s*(注|备注|说明|提示)：\s*$/gm, '');
      text = text.replace(/\*\*(注|备注|说明|提示)：\*\*\s*$/g, '');

      // 2. 清理重复的标签行（如连续的"实验优先级："）
      text = text.replace(/^(.+：)\s*\n\s*\1\s*$/gm, '$1');

      // 3. 清理结尾的不完整句子（以冒号结尾但没有内容）
      text = text.replace(/\n\s*[^：\n]*：\s*$/g, '');

      // 记录清理效果（静默处理）

      // 逐行处理，后端已经处理了所有格式问题
      const lines = text.split('\n');
      let html = '';
      let inList = false;
      let inTable = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (!line.trim()) {
          // 空行处理
          if (inList) {
            html += '</ul>';
            inList = false;
          }
          if (inTable) {
            html += '</tbody></table>';
            inTable = false;
          }
          html += '<br>';
          continue;
        }

        // 🔧 处理Markdown表格
        if (line.includes('|') && line.trim().startsWith('|') && line.trim().endsWith('|')) {
          if (inList) {
            html += '</ul>';
            inList = false;
          }

          // 检查下一行是否是表格分隔符
          const nextLine = i + 1 < lines.length ? lines[i + 1] : '';
          const isHeaderRow = nextLine.includes('|') && nextLine.includes('-');

          if (!inTable) {
            // 开始表格
            html += '<table class="markdown-table" style="width: 100%; border-collapse: collapse; margin: 1em 0; border: 1px solid #ddd;">';
            inTable = true;
          }

          if (isHeaderRow) {
            // 表头行
            html += '<thead><tr>';
            const allCells = line.split('|');
            // 安全地移除首尾空元素，确保至少有3个元素（|cell1|cell2|）
            const cells = allCells.length >= 3 ? allCells.slice(1, -1) : [];

            if (cells.length > 0) {
              cells.forEach(cell => {
                const cellContent = cell ? cell.trim() : '';
                const processedCell = this.processInlineFormats(cellContent);
                html += `<th style="border: 1px solid #ddd; padding: 8px 12px; background-color: #f5f5f5; font-weight: bold; text-align: left;">${processedCell}</th>`;
              });
            }
            html += '</tr></thead><tbody>';
            i++; // 跳过分隔符行
          } else {
            // 数据行
            html += '<tr>';
            const allCells = line.split('|');
            // 安全地移除首尾空元素
            const cells = allCells.length >= 3 ? allCells.slice(1, -1) : [];

            if (cells.length > 0) {
              cells.forEach(cell => {
                const cellContent = cell ? cell.trim() : '';
                const processedCell = this.processInlineFormats(cellContent);
                html += `<td style="border: 1px solid #ddd; padding: 8px 12px;">${processedCell}</td>`;
              });
            }
            html += '</tr>';
          }
          continue;
        }

        // 如果不是表格行但之前在表格中，结束表格
        if (inTable) {
          html += '</tbody></table>';
          inTable = false;
        }

        // 处理标题 (### 开头) - 后端已经确保格式正确
        if (line.startsWith('### ')) {
          if (inList) {
            html += '</ul>';
            inList = false;
          }
          const titleContent = line.substring(4);
          const processedTitle = this.processInlineFormats(titleContent);
          html += `<h3 class="text-h6 font-weight-bold text-primary">${processedTitle}</h3>`;
        }
        // 处理列表项 (- 开头) - 后端已经确保格式正确
        else if (line.startsWith('- ')) {
          const listContent = line.substring(2);
          if (!listContent.trim()) continue;

          if (!inList) {
            html += '<ul>';
            inList = true;
          }

          // 处理列表项中的粗体格式
          const processedListContent = this.processInlineFormats(listContent);
          html += `<li>${processedListContent}</li>`;
        }
        // 处理分隔线
        else if (line.trim() === '---') {
          if (inList) {
            html += '</ul>';
            inList = false;
          }
          html += '<hr style="border-color: #1976d2; opacity: 0.3; margin: 0.3em 0;">';
        }
        // 普通段落
        else {
          if (inList) {
            html += '</ul>';
            inList = false;
          }

          // 处理段落中的粗体格式
          const processedLine = this.processInlineFormats(line);
          html += `<p>${processedLine}</p>`;
        }
      }

      // 关闭未关闭的表格
      if (inTable) {
        html += '</tbody></table>';
      }

      // 关闭未关闭的列表
      if (inList) {
        html += '</ul>';
      }

      return html;
    },

    // 保留原方法作为备用
    formatAnalysisText(text) {
      // 现在直接调用信任后端的方法
      return this.directMarkdownToHTML(text);
    },

    // 极简的markdown格式修复（后端已处理大部分问题）
    fixMarkdownIssues(text) {
      let fixed = text;

      // 后端已经处理了大部分格式问题，前端只做最基本的清理

      // 1. 处理分隔线（如果后端遗漏）
      fixed = fixed.replace(/^---+$/gm, '<hr>');

      // 2. 清理多余空行（如果后端遗漏）
      fixed = fixed.replace(/\n{3,}/g, '\n\n');

      // 3. 确保文本末尾有换行
      if (fixed && !fixed.endsWith('\n')) {
        fixed += '\n';
      }

      // 4. 安全处理：信任后端已经处理了HTML转义
      // 移除前端的HTML转义逻辑，避免双重转义和安全漏洞
      // 后端应该负责确保AI返回内容的安全性

      return fixed;
    },

    // 新增方法：格式化行内markdown元素
    formatInlineMarkdown(text) {
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
        .replace(/`(.*?)`/g, '<code>$1</code>');           // 代码
    },

    // 处理内联格式（粗体、斜体等）
    processInlineFormats(text) {
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
        .replace(/`(.*?)`/g, '<code>$1</code>');           // 代码
    },

    // 清理EventSource连接的专用方法 - 增强版本
    cleanupEventSource() {
      if (this.aiEventSource) {
        try {
          // 先移除所有事件监听器，防止在关闭过程中触发
          this.aiEventSource.onopen = null;
          this.aiEventSource.onmessage = null;
          this.aiEventSource.onerror = null;
          this.aiEventSource.onclose = null;

          // 检查连接状态，只有在连接状态下才关闭
          if (this.aiEventSource.readyState !== EventSource.CLOSED) {
            this.aiEventSource.close();
          }
        } catch (error) {
          console.warn('关闭EventSource时出错:', error);
          // 即使出错也要确保引用被清理
        } finally {
          this.aiEventSource = null;
        }
      }
    },

    // 清理拖拽事件监听器的专用方法
    cleanupDragListeners() {
      if (this.isDragging) {
        document.removeEventListener('mousemove', this.onDrag);
        document.removeEventListener('mouseup', this.stopDrag);
        this.isDragging = false;
      }
    }
  },

  mounted() {
    this.fetchMaterial();
  },

  beforeUnmount() {
    // 统一清理所有资源
    this.cleanupAllResources();
  },
};
</script>

<style>
.material-detail {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
}

.v-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: none !important;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.v-card-title {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

/* 表格样式 */
.custom-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white !important;
  table-layout: fixed;
  margin-bottom: 0;
}

.custom-table tbody tr {
  transition: background-color 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.custom-table tbody tr:last-child {
  border-bottom: none;
}

.custom-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.custom-table td {
  padding: 10px 12px !important;
  line-height: 1.3;
  word-wrap: break-word;
  overflow: hidden;
  vertical-align: middle;
}

.param-label {
  color: rgba(0, 0, 0, 0.7);
  font-weight: 500;
  font-size: 0.9rem;
  white-space: normal;
  width: 30%;
  letter-spacing: 0.01em;
  padding-right: 8px;
}

.param-value {
  color: rgba(0, 0, 0, 0.87);
  font-family: "Roboto", sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: right;
  width: 20%;
  letter-spacing: 0.02em;
  white-space: normal;
}

.param-label-full {
  color: rgba(0, 0, 0, 0.7);
  font-weight: 500;
  font-size: 0.9rem;
  white-space: normal;
  width: 50%;
  letter-spacing: 0.01em;
  padding-right: 8px;
}

.param-value-full {
  color: rgba(0, 0, 0, 0.87);
  font-family: "Roboto", sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: right;
  width: 50%;
  letter-spacing: 0.02em;
  white-space: normal;
}

/* 响应式调整 */
@media (max-width: 960px) {
  .material-detail {
    padding: 16px;
  }

  .custom-table td {
    padding: 8px 10px !important;
    font-size: 0.85rem !important;
  }

  .param-label,
  .param-value {
    font-size: 0.85rem;
    width: auto;
  }

  .v-card-title {
    padding: 16px !important;
  }
}

/* 链接样式 */
a.text-primary {
  transition: opacity 0.2s ease;
}

a.text-primary:hover {
  opacity: 0.8;
  text-decoration: underline !important;
}

/* 3D结构查看器容器 */
.structure-container {
  position: relative;
  min-height: 400px;
}

/* 表格类卡片内容区域样式 */
.table-card .v-card-text {
  padding: 0 !important;
}

/* Crystal Structure卡片保持原有样式 */
.crystal-structure-card .v-card-text {
  padding: 24px !important;
}

/* AI分析窗口样式 */
.ai-floating-window {
  position: fixed;
  z-index: 9999;
  border-radius: 12px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  user-select: text; /* 允许文本选择 */
  background: white;
}

.ai-floating-window:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

.ai-analysis-card {
  border-radius: 12px !important;
  border: none !important;
  overflow: hidden;
  background-color: white !important;
  box-shadow: none !important;
}

.draggable-header {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  min-height: 60px !important;
  border-top-left-radius: 12px !important;
  border-top-right-radius: 12px !important;
}

.draggable-header * {
  color: white !important;
}

/* 加载容器样式 - 确保完美居中，但不阻挡标题栏 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.95);
  pointer-events: none; /* 关键：让鼠标事件穿透，不阻挡拖拽 */
}

.loading-content {
  text-align: center;
  padding: 20px;
  pointer-events: auto; /* 恢复加载内容的鼠标事件，但仍然不会阻挡拖拽 */
}

.ai-analysis-content {
  flex: 1;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 8px;
  margin: 8px 12px;
  border: none;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  user-select: text !important; /* 强制允许文本选择 */
  cursor: text; /* 显示文本光标 */
}

/* 确保AI分析内容中的所有元素都可以被选择 */
.ai-analysis-content *,
.analysis-text *,
.analysis-text {
  user-select: text !important;
}

/* 旧的analysis-text样式已删除，使用下面的优化版本 */



/* 实时markdown格式化样式优化 */
.analysis-text {
  font-family: 'Roboto', sans-serif;
  line-height: 1.4 !important; /* 进一步减少行高 */
  color: #333;
  font-size: 15px;
  user-select: text; /* 确保文本可以被选择 */
  cursor: text; /* 显示文本光标 */
  /* 移除过渡效果，避免样式变化 */
  /* transition: all 0.1s ease-out; */
}

/* 自定义padding，避免Vuetify类的干扰 */
.analysis-text.custom-padding {
  padding: 8px !important; /* 替代pa-2，确保不被覆盖 */
}

/* 确保markdown元素在实时渲染时的样式一致性 */
.analysis-text h1,
.analysis-text h2,
.analysis-text h3,
.analysis-text h4,
.analysis-text h5,
.analysis-text h6 {
  margin-top: 0.5em !important; /* 进一步减少标题上边距 */
  margin-bottom: 0.2em !important; /* 进一步减少标题下边距 */
  font-weight: 600;
  line-height: 1.3 !important; /* 标题行高也要紧凑 */
}

.analysis-text h3 {
  border-bottom: 2px solid #1976d2;
  padding-bottom: 2px; /* 进一步减少内边距 */
}

.analysis-text ul,
.analysis-text ol {
  margin: 0.1em 0 !important; /* 进一步减少列表间距 */
  padding-left: 1.5em;
}

.analysis-text li {
  margin-bottom: 0.05em !important; /* 进一步减少列表项间距 */
  line-height: 1.3 !important; /* 列表项行高也要紧凑 */
}

.analysis-text p {
  margin: 0.1em 0 !important; /* 进一步减少段落间距 */
  text-align: justify;
  line-height: 1.4 !important; /* 段落行高保持一致 */
}

.analysis-text strong {
  color: #1976d2;
  font-weight: 600;
}

.analysis-text em {
  font-style: italic;
  color: #666;
}

/* 分析完成状态的样式锁定 - 确保样式不会变化 */
.analysis-text.analysis-completed {
  line-height: 1.4 !important; /* 锁定行高 */
}

.analysis-text.analysis-completed h1,
.analysis-text.analysis-completed h2,
.analysis-text.analysis-completed h3,
.analysis-text.analysis-completed h4,
.analysis-text.analysis-completed h5,
.analysis-text.analysis-completed h6 {
  margin-top: 0.5em !important; /* 锁定标题间距 */
  margin-bottom: 0.2em !important;
  line-height: 1.3 !important;
}

.analysis-text.analysis-completed ul,
.analysis-text.analysis-completed ol {
  margin: 0.1em 0 !important; /* 锁定列表间距 */
}

.analysis-text.analysis-completed li {
  margin-bottom: 0.05em !important; /* 锁定列表项间距 */
  line-height: 1.3 !important;
}

.analysis-text.analysis-completed p {
  margin: 0.1em 0 !important; /* 锁定段落间距 */
  line-height: 1.4 !important;
}

/* 额外的强制样式锁定 - 防止任何外部样式干扰 */
.analysis-text.analysis-completed * {
  transition: none !important; /* 禁用所有过渡效果 */
}

/* 确保Vuetify的样式不会覆盖我们的设置 */
.analysis-text.analysis-completed.custom-padding {
  padding: 8px !important;
  box-sizing: border-box !important;
}

/* 对话框动画 */
.v-dialog {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 滚动条样式 */
.ai-analysis-content::-webkit-scrollbar {
  width: 6px;
}

.ai-analysis-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-analysis-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ai-analysis-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* 拖拽时的样式 */
.ai-floating-window.dragging {
  transition: none;
  opacity: 0.9;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ai-floating-window {
    width: calc(100vw - 20px) !important;
    height: calc(100vh - 100px) !important;
    left: 10px !important;
    top: 50px !important;
  }
}

/* 🔧 Markdown表格样式 */
.analysis-text table,
.analysis-text .markdown-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  border: 1px solid #ddd;
  font-size: 0.9em;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-text table th,
.analysis-text .markdown-table th {
  border: 1px solid #ddd;
  padding: 12px 16px;
  background-color: #f8f9fa;
  font-weight: 600;
  text-align: left;
  color: #1976d2;
  font-size: 0.95em;
}

.analysis-text table td,
.analysis-text .markdown-table td {
  border: 1px solid #ddd;
  padding: 10px 16px;
  vertical-align: top;
  line-height: 1.5;
}

.analysis-text table tr:nth-child(even),
.analysis-text .markdown-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.analysis-text table tr:hover,
.analysis-text .markdown-table tr:hover {
  background-color: #f0f8ff;
}
</style>
