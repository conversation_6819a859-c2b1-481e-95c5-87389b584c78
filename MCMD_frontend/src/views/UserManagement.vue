<template>
  <v-container fluid class="user-management-page pa-6">
    <div class="content-wrapper">
      <v-row>
        <v-col cols="12">
          <v-card class="mb-6" elevation="0">
            <v-card-title class="d-flex align-center py-4 px-6 bg-gradient">
              <v-icon color="white" class="mr-2" size="28">mdi-account-group</v-icon>
              <span class="text-h5 font-weight-bold white--text">用户管理系统</span>
              <v-spacer></v-spacer>
              <!-- 用户信息显示 -->
              <div class="d-flex align-center">
                <v-chip
                  color="white"
                  variant="flat"
                  size="small"
                  prepend-icon="mdi-account"
                  class="text-black"
                >
                  <span class="font-weight-medium">{{ currentUser || '未登录' }}</span>
                </v-chip>
                <v-chip
                  color="white"
                  variant="outlined"
                  size="small"
                  prepend-icon="mdi-shield-account"
                  class="text-white ml-3"
                  style="border-color: white;"
                >
                  管理员
                </v-chip>
              </div>
            </v-card-title>

            <!-- 统计信息和操作按钮 -->
            <v-card-text class="pa-6">
              <!-- 用户统计卡片 -->
              <v-row class="mb-6">
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="primary" variant="tonal">
                    <v-icon size="48" color="primary" class="mb-2">mdi-account-multiple</v-icon>
                    <div class="text-h4 font-weight-bold">{{ totalUsers }}</div>
                    <div class="text-subtitle-2">总用户数</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="success" variant="tonal">
                    <v-icon size="48" color="success" class="mb-2">mdi-account-check</v-icon>
                    <div class="text-h4 font-weight-bold">{{ activeUsers }}</div>
                    <div class="text-subtitle-2">活跃用户</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="info" variant="tonal">
                    <v-icon size="48" color="info" class="mb-2">mdi-shield-account</v-icon>
                    <div class="text-h4 font-weight-bold">{{ adminUsers }}</div>
                    <div class="text-subtitle-2">管理员</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card class="text-center pa-4" color="warning" variant="tonal">
                    <v-icon size="48" color="warning" class="mb-2">mdi-account-eye</v-icon>
                    <div class="text-h4 font-weight-bold">{{ guestUsers }}</div>
                    <div class="text-subtitle-2">游客用户</div>
                  </v-card>
                </v-col>
              </v-row>

              <!-- 用户列表标题和操作 -->
              <div class="d-flex align-center justify-space-between mb-4">
                <h3 class="text-h6 font-weight-bold">用户列表</h3>
                <v-btn
                  color="primary"
                  prepend-icon="mdi-refresh"
                  @click="loadUsers"
                  :loading="loading"
                  variant="outlined"
                  class="operation-btn"
                >
                  刷新数据
                </v-btn>
              </div>
          <v-data-table
            :headers="headers"
            :items="users"
            :loading="loading"
            class="elevation-0"
            item-key="username"
          >
            <!-- 角色列 -->
            <template v-slot:item.role="{ item }">
              <v-chip
                :color="getRoleColor(item.role)"
                size="small"
                variant="tonal"
              >
                <v-icon start :icon="getRoleIcon(item.role)"></v-icon>
                {{ getRoleText(item.role) }}
              </v-chip>
            </template>

            <!-- 状态列 -->
            <template v-slot:item.status="{ item }">
              <v-chip
                :color="getStatusColor(item.status)"
                size="small"
                variant="tonal"
              >
                <v-icon start :icon="getStatusIcon(item.status)"></v-icon>
                {{ getStatusText(item.status) }}
              </v-chip>
            </template>

            <!-- 创建时间列 -->
            <template v-slot:item.createdAt="{ item }">
              {{ formatDate(item.createdAt) }}
            </template>

            <!-- 最后登录时间列 -->
            <template v-slot:item.lastLoginAt="{ item }">
              {{ item.lastLoginAt ? formatDate(item.lastLoginAt) : '从未登录' }}
            </template>

            <!-- 操作列 -->
            <template v-slot:item.actions="{ item }">
              <div class="d-flex gap-2">
                <!-- 角色管理 -->
                <v-menu>
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-account-cog"
                      size="small"
                      variant="text"
                      v-bind="props"
                      :disabled="item.username === currentUser"
                    ></v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="role in availableRoles"
                      :key="role.value"
                      @click="updateUserRole(item.username, role.value)"
                      :disabled="item.role === role.value"
                    >
                      <template v-slot:prepend>
                        <v-icon :icon="role.icon" :color="role.color"></v-icon>
                      </template>
                      <v-list-item-title>{{ role.text }}</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>

                <!-- 状态管理 -->
                <v-menu>
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-account-switch"
                      size="small"
                      variant="text"
                      v-bind="props"
                      :disabled="item.username === currentUser"
                    ></v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="status in availableStatuses"
                      :key="status.value"
                      @click="updateUserStatus(item.username, status.value)"
                      :disabled="item.status === status.value"
                    >
                      <template v-slot:prepend>
                        <v-icon :icon="status.icon" :color="status.color"></v-icon>
                      </template>
                      <v-list-item-title>{{ status.text }}</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <!-- 确认对话框 -->
    <UnifiedConfirmDialog
      v-model="confirmDialog"
      :title="confirmTitle"
      :message="confirmMessage"
      :type="confirmType"
      :confirm-text="confirmButtonText"
      :custom-alert-message="confirmAlertMessage"
      :show-admin-chip="true"
      @confirm="confirmAction"
      @cancel="confirmDialog = false"
    />

    <!-- 消息提示 -->
    <UnifiedSnackbar
      v-model="snackbar"
      :message="snackbarMessage"
      :color="snackbarColor"
    />
  </v-container>
</template>

<script>
import { userApi, authApi } from '@/utils/api'
import UnifiedSnackbar from '@/components/UnifiedSnackbar.vue'
import UnifiedConfirmDialog from '@/components/UnifiedConfirmDialog.vue'

export default {
  name: 'UserManagement',

  components: {
    UnifiedSnackbar,
    UnifiedConfirmDialog
  },

  data() {
    return {
      users: [],
      loading: false,
      currentUser: '',
      confirmDialog: false,
      confirmTitle: '确认操作',
      confirmMessage: '',
      confirmType: 'warning',
      confirmButtonText: '确认',
      confirmAlertMessage: '',
      confirmAction: null,
      snackbar: false,
      snackbarMessage: '',
      snackbarColor: 'success',
      
      headers: [
        { title: '用户名', key: 'username', sortable: true },
        { title: '邮箱', key: 'email', sortable: true },
        { title: '真实姓名', key: 'realName', sortable: true },
        { title: '机构', key: 'organization', sortable: true },
        { title: '角色', key: 'role', sortable: true },
        { title: '状态', key: 'status', sortable: true },
        { title: '创建时间', key: 'createdAt', sortable: true },
        { title: '最后登录', key: 'lastLoginAt', sortable: true },
        { title: '操作', key: 'actions', sortable: false }
      ],

      availableRoles: [
        { value: 'guest', text: '游客用户', icon: 'mdi-account-eye', color: 'warning' },
        { value: 'user', text: '普通用户', icon: 'mdi-account', color: 'info' },
        { value: 'admin', text: '管理员', icon: 'mdi-shield-account', color: 'success' }
      ],

      availableStatuses: [
        { value: 'active', text: '激活', icon: 'mdi-account-check', color: 'success' },
        { value: 'banned', text: '已禁用', icon: 'mdi-account-cancel', color: 'error' }
      ]
    }
  },

  computed: {
    totalUsers() {
      return this.users.length
    },

    activeUsers() {
      return this.users.filter(user => user.status === 'active').length
    },

    adminUsers() {
      return this.users.filter(user => user.role === 'admin').length
    },

    guestUsers() {
      return this.users.filter(user => user.role === 'guest').length
    }
  },

  async created() {
    await this.getCurrentUser()
    await this.loadUsers()
  },

  methods: {
    async getCurrentUser() {
      try {
        const response = await authApi.getCurrentUserInfo()
        this.currentUser = response.username
      } catch (error) {
        console.error('获取当前用户信息失败:', error)
      }
    },

    async loadUsers() {
      this.loading = true
      try {
        const response = await userApi.getAllUsers()
        this.users = response.users || []
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.showMessage('加载用户列表失败', 'error')
      } finally {
        this.loading = false
      }
    },

    updateUserRole(username, newRole) {
      const roleText = this.getRoleText(newRole)
      this.confirmTitle = '确认角色更改'
      this.confirmMessage = `确定要将用户 "${username}" 的角色更改为 "${roleText}" 吗？`
      this.confirmType = 'info'
      this.confirmButtonText = '确认更改'
      this.confirmAlertMessage = '角色更改后用户的权限将立即生效，如有需要可以随时调整。'
      this.confirmAction = async () => {
        try {
          await userApi.updateUserRole(username, newRole)
          this.showMessage(`用户角色更新成功`, 'success')
          await this.loadUsers()
        } catch (error) {
          console.error('更新用户角色失败:', error)
          this.showMessage('更新用户角色失败', 'error')
        }
        this.confirmDialog = false
      }
      this.confirmDialog = true
    },

    updateUserStatus(username, newStatus) {
      const statusText = this.getStatusText(newStatus)

      if (newStatus === 'banned') {
        this.confirmTitle = '确认禁用用户'
        this.confirmMessage = `确定要禁用用户 "${username}" 吗？`
        this.confirmType = 'error'
        this.confirmButtonText = '确认禁用'
        this.confirmAlertMessage = '禁用后用户将无法登录系统，但可以随时重新激活。'
      } else {
        this.confirmTitle = '确认状态更改'
        this.confirmMessage = `确定要将用户 "${username}" 的状态更改为 "${statusText}" 吗？`
        this.confirmType = 'info'
        this.confirmButtonText = '确认更改'
        this.confirmAlertMessage = '状态更改后将立即生效，如有需要可以随时调整。'
      }

      this.confirmAction = async () => {
        try {
          await userApi.updateUserStatus(username, newStatus)
          this.showMessage(`用户状态更新成功`, 'success')
          await this.loadUsers()
        } catch (error) {
          console.error('更新用户状态失败:', error)
          this.showMessage('更新用户状态失败', 'error')
        }
        this.confirmDialog = false
      }
      this.confirmDialog = true
    },

    getRoleText(role) {
      const roleMap = {
        'guest': '游客用户',
        'user': '普通用户',
        'admin': '管理员'
      }
      return roleMap[role] || role
    },

    getRoleColor(role) {
      const colorMap = {
        'guest': 'warning',
        'user': 'info',
        'admin': 'success'
      }
      return colorMap[role] || 'default'
    },

    getRoleIcon(role) {
      const iconMap = {
        'guest': 'mdi-account-eye',
        'user': 'mdi-account',
        'admin': 'mdi-shield-account'
      }
      return iconMap[role] || 'mdi-account'
    },

    getStatusText(status) {
      const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'banned': '已禁用'
      }
      return statusMap[status] || status
    },

    getStatusColor(status) {
      const colorMap = {
        'active': 'success',
        'inactive': 'warning',
        'banned': 'error'
      }
      return colorMap[status] || 'default'
    },

    getStatusIcon(status) {
      const iconMap = {
        'active': 'mdi-account-check',
        'inactive': 'mdi-account-clock',
        'banned': 'mdi-account-cancel'
      }
      return iconMap[status] || 'mdi-account'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    showMessage(message, color = 'success') {
      this.snackbarMessage = message
      this.snackbarColor = color
      this.snackbar = true
    }
  }
}
</script>

<style scoped>
.user-management-page {
  background-color: #F5F5F5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.operation-btn {
  min-width: 120px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.gap-2 {
  gap: 8px;
}

/* 背景渐变 */
.bg-gradient {
  background: linear-gradient(135deg, #1976d2, #42a5f5) !important;
}

.white--text {
  color: white !important;
}

/* 统计卡片样式 */
.v-card.v-theme--light.v-card--variant-tonal {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.v-card.v-theme--light.v-card--variant-tonal:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* 数据表格样式 */
.v-data-table {
  border-radius: 8px !important;
}

.v-data-table .v-data-table__thead {
  background-color: rgba(25, 118, 210, 0.05);
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}
</style>
