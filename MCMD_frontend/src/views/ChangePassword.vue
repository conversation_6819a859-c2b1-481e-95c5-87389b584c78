<template>
  <div class="change-password-page">
    <v-container>
      <v-row justify="center">
        <v-col cols="12" md="8" lg="6">
          <v-card class="elevation-4 rounded-lg change-password-card">
            <v-card-title class="text-h5 pa-6 bg-primary text-white">
              <v-icon class="mr-2">mdi-lock-reset</v-icon>
              Change Password
            </v-card-title>

            <v-card-text class="pa-6">
              <v-form @submit.prevent="changePassword" v-model="isValid" ref="form">
                <v-text-field
                  v-model="currentPassword"
                  label="Current Password"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showCurrentPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showCurrentPassword = !showCurrentPassword"
                  :type="showCurrentPassword ? 'text' : 'password'"
                  variant="outlined"
                  :rules="[v => !!v || 'Please enter your current password']"
                  required
                  class="mb-4"
                  density="comfortable"
                ></v-text-field>

                <v-text-field
                  v-model="newPassword"
                  label="New Password"
                  prepend-inner-icon="mdi-lock-plus"
                  :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showNewPassword = !showNewPassword"
                  :type="showNewPassword ? 'text' : 'password'"
                  variant="outlined"
                  :rules="passwordRules"
                  required
                  class="mb-4"
                  density="comfortable"
                  hint="Password must be at least 6 characters"
                  persistent-hint
                ></v-text-field>

                <v-text-field
                  v-model="confirmPassword"
                  label="Confirm New Password"
                  prepend-inner-icon="mdi-lock-check"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  variant="outlined"
                  :rules="confirmPasswordRules"
                  required
                  class="mb-4"
                  density="comfortable"
                ></v-text-field>

                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ error }}
                </v-alert>

                <v-alert
                  v-if="success"
                  type="success"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ success }}
                </v-alert>

                <div class="d-flex gap-3">
                  <v-btn
                    type="submit"
                    color="primary"
                    size="large"
                    :loading="loading"
                    :disabled="!isValid || loading"
                    class="flex-grow-1"
                    elevation="2"
                  >
                    Change Password
                    <template v-slot:loader>
                      <v-progress-circular
                        indeterminate
                        color="white"
                        size="20"
                      ></v-progress-circular>
                    </template>
                  </v-btn>

                  <v-btn
                    color="grey"
                    size="large"
                    variant="outlined"
                    @click="resetForm"
                    :disabled="loading"
                  >
                    Reset
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ChangePassword',

  data() {
    return {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      error: '',
      success: '',
      loading: false,
      isValid: false,
      showCurrentPassword: false,
      showNewPassword: false,
      showConfirmPassword: false
    }
  },

  computed: {
    passwordRules() {
      return [
        v => !!v || 'Please enter a new password',
        v => (v && v.length >= 6) || 'Password must be at least 6 characters',
        v => v !== this.currentPassword || 'New password must be different from current password'
      ]
    },
    confirmPasswordRules() {
      return [
        v => !!v || 'Please confirm your new password',
        v => v === this.newPassword || 'Passwords do not match'
      ]
    }
  },

  methods: {
    async changePassword() {
      if (!this.isValid) return

      this.loading = true
      this.error = ''
      this.success = ''

      try {
        const response = await axios.post('/auth/change-password', {
          currentPassword: this.currentPassword,
          newPassword: this.newPassword
        })

        if (response.data.success) {
          this.success = 'Password updated successfully!'
          this.resetForm()
        } else {
          this.error = response.data.message || 'Unable to update password. Please try again'
        }
      } catch (error) {
        console.error('Password change failed:', error)
        this.error = error.response?.data?.message || 'Unable to update password. Please check your current password'
      } finally {
        this.loading = false
      }
    },

    resetForm() {
      this.currentPassword = ''
      this.newPassword = ''
      this.confirmPassword = ''
      this.error = ''
      this.success = ''
      this.$refs.form?.resetValidation()
    }
  }
}
</script>

<style scoped>
.change-password-page {
  padding: 2rem 0;
  min-height: calc(100vh - 64px);
}

.change-password-card {
  border-radius: 16px !important;
  overflow: hidden;
  border: none !important;
}

/* 输入框样式 */
:deep(.v-field) {
  border-radius: 8px !important;
}

:deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.gap-3 {
  gap: 12px;
}
</style>
