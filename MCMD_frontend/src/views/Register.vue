<template>
  <div class="register-page bg-grey-lighten-4">
    <v-container fluid class="fill-height">
      <v-row align="center" justify="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card class="elevation-4 rounded-lg register-card animate-card">
            <!-- Logo区域 -->
            <div class="text-center pt-6">
              <v-avatar
                color="primary"
                size="64"
                class="mb-3"
              >
                <span class="text-h5 font-weight-bold white--text">M</span>
              </v-avatar>
              <h1 class="text-h4 font-weight-bold mb-1">Join <PERSON></h1>
              <p class="text-subtitle-1 text-medium-emphasis">
                Create your MCMD account to start using Magnetocaloric Materials Database
              </p>
            </div>

            <v-card-text class="pt-4">
              <v-form @submit.prevent="register" v-model="isValid" ref="form">
                <!-- 第一行：用户名和邮箱 -->
                <v-row class="animate-row" style="animation-delay: 0.05s">
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.username"
                      label="Username *"
                      prepend-inner-icon="mdi-account"
                      variant="outlined"
                      :rules="usernameRules"
                      required

                      density="comfortable"
                      bg-color="grey-lighten-5"
                      :loading="checkingUsername"
                      @blur="checkUsername"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.email"
                      label="Email *"
                      prepend-inner-icon="mdi-email"
                      variant="outlined"
                      :rules="emailRules"
                      required

                      density="comfortable"
                      bg-color="grey-lighten-5"
                      :loading="checkingEmail"
                      @blur="checkEmail"
                    ></v-text-field>
                  </v-col>
                </v-row>

                <!-- 第二行：密码和确认密码 -->
                <v-row class="animate-row" style="animation-delay: 0.1s">
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.password"
                      label="Password *"
                      prepend-inner-icon="mdi-lock"
                      :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                      @click:append-inner="showPassword = !showPassword"
                      :type="showPassword ? 'text' : 'password'"
                      variant="outlined"
                      :rules="passwordRules"
                      required

                      density="comfortable"
                      bg-color="grey-lighten-5"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.confirmPassword"
                      label="Confirm Password *"
                      prepend-inner-icon="mdi-lock-check"
                      :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                      @click:append-inner="showConfirmPassword = !showConfirmPassword"
                      :type="showConfirmPassword ? 'text' : 'password'"
                      variant="outlined"
                      :rules="confirmPasswordRules"
                      required

                      density="comfortable"
                      bg-color="grey-lighten-5"
                    ></v-text-field>
                  </v-col>
                </v-row>

                <!-- 第三行：真实姓名和机构 -->
                <v-row class="animate-row" style="animation-delay: 0.15s">
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.realName"
                      label="Real Name"
                      prepend-inner-icon="mdi-account-circle"
                      variant="outlined"

                      density="comfortable"
                      bg-color="grey-lighten-5"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.organization"
                      label="Organization"
                      prepend-inner-icon="mdi-domain"
                      variant="outlined"

                      density="comfortable"
                      bg-color="grey-lighten-5"
                    ></v-text-field>
                  </v-col>
                </v-row>

                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ error }}
                </v-alert>

                <v-alert
                  v-if="success"
                  type="success"
                  variant="tonal"
                  class="mb-4"
                  closable
                  density="comfortable"
                >
                  {{ success }}
                </v-alert>

                <!-- 权限申请提示 -->
                <div class="animate-row" style="animation-delay: 0.2s">
                  <v-alert
                    type="info"
                    variant="tonal"
                    density="comfortable"
                    class="mb-4"
                    border="start"
                  >
                    <div class="text-body-2">
                      <strong>Need data upload permissions?</strong>
                    </div>
                    <div class="text-body-2 mt-1">
                      New users are registered as guest users by default. If you need permissions to upload or modify database content, please contact:
                      <a href="mailto:<EMAIL>" class="text-primary font-weight-medium"><EMAIL></a>
                    </div>
                  </v-alert>
                </div>

                <div class="animate-row" style="animation-delay: 0.25s">
                  <v-btn
                    type="submit"
                    color="primary"
                    block
                    size="large"
                    :loading="loading"
                    :disabled="!isValid || loading"
                    class="mb-2 register-btn"
                    elevation="2"
                    height="48"
                  >
                    Create Account
                    <template v-slot:loader>
                      <v-progress-circular
                        indeterminate
                        color="white"
                        size="20"
                      ></v-progress-circular>
                    </template>
                  </v-btn>
                </div>

                <div class="text-center mt-4">
                  <p class="text-body-2 text-medium-emphasis">
                    Already have an account?
                    <router-link to="/login" class="text-primary text-decoration-none">
                      Sign in now
                    </router-link>
                  </p>
                </div>
              </v-form>
            </v-card-text>

            <v-card-text class="text-center pb-6 text-medium-emphasis">
              <p class="text-caption">
                MCMD@TCPM2
              </p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { authApi } from '@/utils/api'

export default {
  name: 'Register',

  data() {
    return {
      formData: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        realName: '',
        organization: ''
      },
      error: '',
      success: '',
      loading: false,
      showPassword: false,
      showConfirmPassword: false,
      isValid: false,
      checkingUsername: false,
      checkingEmail: false,
      usernameAvailable: null,
      emailAvailable: null
    }
  },

  computed: {
    usernameRules() {
      return [
        v => !!v || 'Username is required',
        v => (v && v.length >= 3) || 'Username must be at least 3 characters',
        v => (v && v.length <= 20) || 'Username cannot exceed 20 characters',
        v => /^[a-zA-Z0-9_]+$/.test(v) || 'Username can only contain letters, numbers and underscores',
        () => this.usernameAvailable !== false || 'Username already exists'
      ]
    },

    emailRules() {
      return [
        v => !!v || 'Email is required',
        v => /.+@.+\..+/.test(v) || 'Email format is invalid',
        () => this.emailAvailable !== false || 'Email already registered'
      ]
    },

    passwordRules() {
      return [
        v => !!v || 'Password is required',
        v => (v && v.length >= 6) || 'Password must be at least 6 characters',
        v => (v && v.length <= 50) || 'Password cannot exceed 50 characters'
      ]
    },

    confirmPasswordRules() {
      return [
        v => !!v || 'Please confirm password',
        v => v === this.formData.password || 'Passwords do not match'
      ]
    }
  },

  methods: {
    async checkUsername() {
      if (!this.formData.username || this.formData.username.length < 3) {
        this.usernameAvailable = null
        return
      }

      this.checkingUsername = true
      try {
        const response = await authApi.checkUsername(this.formData.username)
        this.usernameAvailable = response.available
      } catch (error) {
        console.error('Check username failed:', error)
        this.usernameAvailable = null
      } finally {
        this.checkingUsername = false
      }
    },

    async checkEmail() {
      if (!this.formData.email || !/.+@.+\..+/.test(this.formData.email)) {
        this.emailAvailable = null
        return
      }

      this.checkingEmail = true
      try {
        const response = await authApi.checkEmail(this.formData.email)
        this.emailAvailable = response.available
      } catch (error) {
        console.error('Check email failed:', error)
        this.emailAvailable = null
      } finally {
        this.checkingEmail = false
      }
    },

    async register() {
      if (!this.isValid) return

      this.loading = true
      this.error = ''
      this.success = ''

      try {
        const response = await authApi.register(this.formData)

        if (response.message === 'Registration successful' || response.message === '注册成功') {
          this.success = 'Registration successful! You are now a guest user and can view database content. Please contact an administrator to upgrade your permissions for more features.'

          // Redirect to login page after 3 seconds
          setTimeout(() => {
            this.$router.push('/login')
          }, 3000)
        } else {
          this.error = response.error || 'Registration failed, please try again'
        }
      } catch (error) {
        console.error('Registration failed:', error)
        this.error = error.response?.data?.error || 'Registration failed, please try again'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.register-page {
  min-height: calc(100vh - 64px);
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding-top: 40px;
}

.register-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 16px !important;
  overflow: hidden;
  border: none !important;
}

.register-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.register-btn {
  letter-spacing: 0.5px;
}

/* 卡片进入动画 - Vuetify 风格 */
.animate-card {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 行动画 - 统一方向和风格 */
.animate-row {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) both;
}

/* 使用相同的关键帧，保持一致性 */

/* 输入框样式 */
:deep(.v-field) {
  border-radius: 8px !important;
}

:deep(.v-field__outline) {
  border-width: 1px !important;
}

:deep(.v-field--variant-outlined.v-field--focused .v-field__outline) {
  border-width: 2px !important;
}

/* 链接样式 */
.text-primary {
  font-weight: 500;
}

.text-primary:hover {
  text-decoration: underline !important;
}

/* 权限提示样式 */
.v-alert a {
  text-decoration: none;
}

.v-alert a:hover {
  text-decoration: underline !important;
}
</style>
