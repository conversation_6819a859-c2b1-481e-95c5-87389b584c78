<template>
  <v-container class="about-container py-8">
    <v-row>
      <v-col cols="12">
        <!-- About MCMD Card -->
        <v-card class="rounded-lg elevation-2">
          <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
            <v-icon color="white" class="mr-2">mdi-information</v-icon>
            <span class="text-h5 font-weight-bold white--text">About MCMD</span>
          </v-card-title>
          <v-card-text class="pa-6">
            <p class="text-body-1 mb-6" style="line-height: 1.6">
              The Magnetocaloric Materials Database (MCMD) is a comprehensive resource for researchers and engineers working in the field of magnetic refrigeration and magnetocaloric materials. Our platform provides powerful tools for data management, visualization, and analysis to accelerate research in this field.
            </p>
            <p class="text-subtitle-1 font-weight-medium mb-4">
              Our database provides detailed information about various magnetocaloric materials, including:
            </p>
            <v-list class="bg-grey-lighten-4 rounded-lg">
              <v-list-item
                v-for="(item, index) in features"
                :key="index"
                class="py-2"
              >
                <template v-slot:prepend>
                  <v-icon color="primary" class="mr-2">{{ item.icon }}</v-icon>
                </template>
                <v-list-item-title class="text-body-1">{{ item.title }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>

        <!-- Features Card -->
        <v-card class="mt-6 rounded-lg elevation-2">
          <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
            <v-icon color="white" class="mr-2">mdi-star</v-icon>
            <span class="text-h5 font-weight-bold white--text">Key Features</span>
          </v-card-title>
          <v-card-text class="pa-6">
            <v-row>
              <v-col v-for="(feature, index) in keyFeatures" :key="index" cols="12" md="6" class="mb-4">
                <div class="feature-item pa-4 rounded-lg bg-grey-lighten-4">
                  <div class="d-flex align-center mb-3">
                    <v-avatar color="primary" size="40" class="mr-4">
                      <v-icon color="white">{{ feature.icon }}</v-icon>
                    </v-avatar>
                    <h3 class="text-h6 font-weight-bold mb-0">{{ feature.title }}</h3>
                  </div>
                  <p class="text-body-2 mb-0" style="line-height: 1.5">{{ feature.description }}</p>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Who We Are Card -->
        <v-card class="mt-6 rounded-lg elevation-2">
          <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
            <v-icon color="white" class="mr-2">mdi-account-group</v-icon>
            <span class="text-h5 font-weight-bold white--text">Who We Are</span>
          </v-card-title>
          <v-card-text class="pa-6">
            <div class="d-flex align-center">
              <a 
                href="https://tcmp2.ucas.ac.cn/" 
                target="_blank" 
                class="lab-title text-decoration-none d-inline-flex align-center"
              >
                Theoretical Condensed Matter Physics & Computational Materials Physics Laboratory
                <v-icon 
                  size="small" 
                  class="ml-2"
                >
                  mdi-open-in-new
                </v-icon>
              </a>
            </div>
          </v-card-text>
        </v-card>

        <!-- Contact Us Card -->
        <v-card class="mt-6 rounded-lg elevation-2">
          <v-card-title class="d-flex align-center py-4 px-6 bg-primary">
            <v-icon color="white" class="mr-2">mdi-email</v-icon>
            <span class="text-h5 font-weight-bold white--text">Contact Us</span>
          </v-card-title>
          <v-card-text class="pa-6">
            <v-list class="contact-list">
              <v-list-item
                v-for="(contact, index) in contacts"
                :key="index"
                :class="{ 'mt-4': index > 0 }"
                class="rounded-lg"
              >
                <template v-slot:prepend>
                  <v-avatar color="primary" size="40">
                    <span class="text-h6 white--text">{{ contact.name[0] }}</span>
                  </v-avatar>
                </template>
                <v-list-item-title class="font-weight-medium text-h6 mb-1">
                  {{ contact.name }}
                </v-list-item-title>
                <v-list-item-subtitle>
                  <a 
                    :href="'mailto:' + contact.email" 
                    class="contact-email text-decoration-none d-flex align-center"
                  >
                    <v-icon size="small" class="mr-2">mdi-email</v-icon>
                    {{ contact.email }}
                  </a>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  name: 'About',
  data: () => ({
    features: [
      { title: 'Crystal structures and space groups', icon: 'mdi-crystal-ball' },
      { title: 'Magnetic properties and parameters', icon: 'mdi-magnet' },
      { title: 'Phase transition temperatures', icon: 'mdi-chart-bell-curve' },
      { title: 'XRD patterns for multiple wavelengths (CuKa, CuKa1, CuKa2)', icon: 'mdi-chart-line' },
      { title: 'Magnetic lattice information', icon: 'mdi-grid' },
      { title: 'Density and molar properties', icon: 'mdi-atom' }
    ],
    keyFeatures: [
      { 
        title: 'Advanced Search', 
        icon: 'mdi-magnify', 
        description: 'Search materials by exact element matches or element inclusion with our intuitive search system. Filter by space group number and magnetic lattice.'
      },
      { 
        title: 'Interactive Periodic Table', 
        icon: 'mdi-table-large', 
        description: 'Find materials containing specific elements easily using our interactive periodic table interface.'
      },
      { 
        title: '3D Structure Visualization', 
        icon: 'mdi-rotate-3d', 
        description: 'Visualize crystal structures in 3D with interactive controls for better understanding of material properties.'
      },
      { 
        title: 'XRD Pattern Analysis', 
        icon: 'mdi-chart-bell-curve-cumulative', 
        description: 'View X-ray diffraction patterns for materials with support for multiple wavelengths.'
      },
      { 
        title: 'AI Assistant', 
        icon: 'mdi-chat-processing', 
        description: 'Get help with your research using our integrated AI chat assistant, available to authenticated users.'
      },
      { 
        title: 'Database Management', 
        icon: 'mdi-database-cog', 
        description: 'For administrators: comprehensive tools to add, modify, and manage material data in the database.'
      }
    ],
    contacts: [
    { 
        name: 'C.-W. Zhang',
        email: '<EMAIL>'
      },
      {
        name: 'Z.-Y. Qi',
        email: '<EMAIL>'
      },
      {
        name: 'Q.-B Yan',
        email: '<EMAIL>'
      }
    ]
  })
}
</script>

<style scoped>
.about-container {
  max-width: 1200px;
  margin: 0 auto;
}

.v-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: none !important;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.lab-title {
  font-size: 1.5rem;
  font-weight: 500;
  color: inherit;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.lab-title:hover {
  color: var(--v-primary-base);
}

.lab-title .v-icon {
  opacity: 0.7;
  transition: all 0.3s ease;
}

.lab-title:hover .v-icon {
  opacity: 1;
  transform: translateX(2px);
}

.contact-list {
  background: transparent !important;
}

.contact-list .v-list-item {
  transition: all 0.3s ease;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
}

.contact-list .v-list-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.contact-email {
  color: var(--v-primary-base);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.contact-email:hover {
  opacity: 1;
  text-decoration: underline !important;
}

.v-list-item-subtitle {
  margin-top: 4px !important;
}

.bg-primary {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.feature-item {
  transition: all 0.3s ease;
  height: 100%;
  border-left: 4px solid var(--v-primary-base);
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background-color: rgba(0, 0, 0, 0.02) !important;
}
</style>
