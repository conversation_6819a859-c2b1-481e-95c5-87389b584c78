<template>
  <div class="registration-success-page bg-grey-lighten-4">
    <v-container fluid class="fill-height">
      <v-row align="center" justify="center">
        <v-col cols="12" sm="8" md="6" lg="5">
          <v-card class="elevation-4 rounded-lg success-card animate-card">
            <!-- Logo区域 -->
            <div class="text-center pt-6">
              <v-avatar
                color="success"
                size="80"
                class="mb-4"
              >
                <v-icon size="40" color="white">mdi-check-circle</v-icon>
              </v-avatar>
              <h1 class="text-h4 font-weight-bold mb-2 text-success">Account Created!</h1>
              <p class="text-subtitle-1 text-medium-emphasis">
                Welcome to the MCMD community
              </p>
            </div>

            <v-card-text class="pt-4 pb-6">
              <div class="text-center">
                <!-- 邮箱验证提示 -->
                <div v-if="needVerification" class="mb-6">
                  <v-icon color="info" size="48" class="mb-3">mdi-email-check</v-icon>
                  <h2 class="text-h5 mb-3">Verify Your Email Address</h2>
                  <p class="text-body-1 mb-4">
                    We've sent a verification email to:
                  </p>
                  <div class="email-display mb-4">
                    <v-chip
                      color="primary"
                      variant="tonal"
                      size="large"
                      class="px-4 py-2"
                    >
                      <v-icon left>mdi-email</v-icon>
                      {{ email }}
                    </v-chip>
                  </div>
                  <p class="text-body-2 text-medium-emphasis mb-6">
                    Please check your email and click the verification link to activate your account.
                    The link will expire in 24 hours.
                  </p>
                  
                  <!-- 重新发送邮件 -->
                  <div class="mb-4">
                    <v-btn
                      color="primary"
                      variant="outlined"
                      @click="resendVerificationEmail"
                      :loading="resending"
                      class="mb-3"
                    >
                      <v-icon left>mdi-email-send</v-icon>
                      Resend Verification Email
                    </v-btn>
                  </div>
                  
                  <!-- 重新发送结果 -->
                  <v-alert
                    v-if="resendResult"
                    :type="resendResult.success ? 'success' : 'error'"
                    variant="tonal"
                    class="mb-4"
                  >
                    {{ resendResult.message }}
                  </v-alert>
                </div>

                <!-- 直接激活的用户 -->
                <div v-else class="mb-6">
                  <v-icon color="success" size="48" class="mb-3">mdi-account-check</v-icon>
                  <h2 class="text-h5 mb-3">Account Ready!</h2>
                  <p class="text-body-1 mb-4">
                    Welcome, <strong>{{ username }}</strong>!
                  </p>
                  <p class="text-body-2 text-medium-emphasis mb-4">
                    Your account has been created as a guest user. You can now browse the database.
                    To upload or modify data, please contact an administrator.
                  </p>
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex flex-column gap-3">
                  <v-btn
                    color="primary"
                    size="large"
                    variant="elevated"
                    @click="goToLogin"
                    class="mb-2"
                  >
                    <v-icon left>mdi-login</v-icon>
                    Sign In Now
                  </v-btn>
                  
                  <v-btn
                    color="grey"
                    variant="text"
                    @click="goToHome"
                  >
                    <v-icon left>mdi-home</v-icon>
                    Go to Homepage
                  </v-btn>
                </div>

                <!-- 帮助信息 -->
                <v-divider class="my-6"></v-divider>
                
                <div class="help-section">
                  <h3 class="text-h6 mb-3">Need Help?</h3>
                  <div class="text-body-2 text-medium-emphasis">
                    <p class="mb-2">
                      <v-icon size="16" class="mr-1">mdi-email</v-icon>
                      Didn't receive the email? Check your spam folder
                    </p>
                    <p class="mb-2">
                      <v-icon size="16" class="mr-1">mdi-help-circle</v-icon>
                      Need data upload permissions? Contact:
                      <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>
                    </p>
                    <p>
                      <v-icon size="16" class="mr-1">mdi-information</v-icon>
                      Having trouble? Visit our help center or contact support
                    </p>
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'RegistrationSuccess',
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const username = ref('')
    const email = ref('')
    const needVerification = ref(false)
    const resending = ref(false)
    const resendResult = ref(null)
    
    const resendVerificationEmail = async () => {
      resending.value = true
      resendResult.value = null
      
      try {
        const response = await axios.post('/api/email-verification/resend', {
          email: email.value
        })
        resendResult.value = response.data
      } catch (error) {
        resendResult.value = {
          success: false,
          message: error.response?.data?.message || 'Unable to send verification email. Please try again'
        }
      } finally {
        resending.value = false
      }
    }
    
    const goToLogin = () => {
      router.push('/login')
    }
    
    const goToHome = () => {
      router.push('/')
    }
    
    onMounted(() => {
      // 从路由参数获取注册信息
      username.value = route.query.username || ''
      email.value = route.query.email || ''
      needVerification.value = route.query.needVerification === 'true'
      
      // 如果没有必要的参数，重定向到注册页面
      if (!username.value) {
        router.push('/register')
      }
    })
    
    return {
      username,
      email,
      needVerification,
      resending,
      resendResult,
      resendVerificationEmail,
      goToLogin,
      goToHome
    }
  }
}
</script>

<style scoped>
.registration-success-page {
  min-height: 100vh;
}

.success-card {
  max-width: 600px;
  margin: 0 auto;
}

.animate-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.email-display {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gap-3 {
  gap: 12px;
}

.help-section {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}
</style>
