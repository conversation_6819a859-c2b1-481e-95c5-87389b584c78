<template>
  <div class="forgot-password-page bg-grey-lighten-4">
    <v-container fluid class="fill-height">
      <v-row align="center" justify="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card class="elevation-4 rounded-lg forgot-password-card animate-card">
            <!-- Logo区域 -->
            <div class="text-center pt-6">
              <v-avatar
                color="primary"
                size="64"
                class="mb-3"
              >
                <span class="text-h5 font-weight-bold white--text">M</span>
              </v-avatar>
              <h1 class="text-h4 font-weight-bold mb-1">Forgot Password</h1>
              <p class="text-subtitle-1 text-medium-emphasis">
                Enter your username or email, and we'll send a password reset link to your email
              </p>
            </div>

            <v-card-text class="pt-4">
              <v-form @submit.prevent="sendResetEmail" v-model="isValid" ref="form">
                <div class="animate-row" style="animation-delay: 0.05s">
                  <v-text-field
                    v-model="usernameOrEmail"
                    label="Username or Email"
                    prepend-inner-icon="mdi-account"
                    variant="outlined"
                    :rules="[v => !!v || 'Please enter username or email']"
                    required
                    class="mb-4"
                    density="comfortable"
                    bg-color="grey-lighten-5"
                    hint="Please enter the username or email address used during registration"
                    persistent-hint
                  ></v-text-field>
                </div>

                <div class="animate-row" style="animation-delay: 0.1s">
                  <v-alert
                    v-if="error"
                    type="error"
                    variant="tonal"
                    class="mb-4"
                    closable
                    density="comfortable"
                  >
                    {{ error }}
                  </v-alert>

                  <v-alert
                    v-if="success"
                    type="success"
                    variant="tonal"
                    class="mb-4"
                    closable
                    density="comfortable"
                  >
                    {{ success }}
                  </v-alert>
                </div>

                <div class="animate-row" style="animation-delay: 0.15s">
                  <v-btn
                    type="submit"
                    color="primary"
                    block
                    size="large"
                    :loading="loading"
                    :disabled="!isValid || loading"
                    class="mb-4 reset-btn"
                    elevation="2"
                    height="48"
                  >
                    Send Reset Email
                    <template v-slot:loader>
                      <v-progress-circular
                        indeterminate
                        color="white"
                        size="20"
                      ></v-progress-circular>
                    </template>
                  </v-btn>
                </div>

                <div class="text-center animate-row" style="animation-delay: 0.2s">
                  <p class="text-body-2 text-medium-emphasis">
                    Remembered your password?
                    <router-link to="/login" class="text-primary text-decoration-none">
                      Sign In
                    </router-link>
                  </p>
                  <p class="text-body-2 text-medium-emphasis mt-2">
                    Don't have an account?
                    <router-link to="/register" class="text-primary text-decoration-none">
                      Create Account
                    </router-link>
                  </p>
                </div>
              </v-form>
            </v-card-text>

            <v-card-text class="text-center pb-6 text-medium-emphasis">
              <p class="text-caption">
                MCMD@TCMP2
              </p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ForgotPassword',

  data() {
    return {
      usernameOrEmail: '',
      error: '',
      success: '',
      loading: false,
      isValid: false
    }
  },

  methods: {
    async sendResetEmail() {
      if (!this.isValid) return

      this.loading = true
      this.error = ''
      this.success = ''

      try {
        const response = await axios.post('/auth/forgot-password', {
          usernameOrEmail: this.usernameOrEmail
        })

        if (response.data.success) {
          this.success = 'Password reset email has been sent to your email address. Please check your email and follow the instructions to reset your password.'
          this.usernameOrEmail = ''
        } else {
          this.error = response.data.message || 'Failed to send email, please try again later'
        }
      } catch (error) {
        console.error('Failed to send reset email:', error)
        this.error = error.response?.data?.message || 'Failed to send email, please check if the username or email is correct'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.forgot-password-page {
  min-height: calc(100vh - 64px);
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding-top: 80px;
}

.forgot-password-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 16px !important;
  overflow: hidden;
  border: none !important;
}

.forgot-password-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.reset-btn {
  letter-spacing: 0.5px;
}

/* 卡片进入动画 */
.animate-card {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 行动画 - 统一方向和风格 */
.animate-row {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) both;
}

/* 输入框样式 */
:deep(.v-field) {
  border-radius: 8px !important;
}

:deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* 链接样式 */
.text-primary {
  font-weight: 500;
  transition: color 0.2s ease;
}

.text-primary:hover {
  color: rgb(var(--v-theme-primary)) !important;
  text-decoration: underline !important;
}
</style>
