<template>
  <div class="verification-page bg-grey-lighten-4">
    <v-container fluid class="fill-height">
      <v-row align="center" justify="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card class="elevation-4 rounded-lg verification-card animate-card">
            <!-- Logo区域 -->
            <div class="text-center pt-6">
              <v-avatar
                color="primary"
                size="64"
                class="mb-3"
              >
                <span class="text-h5 font-weight-bold white--text">M</span>
              </v-avatar>
              <h1 class="text-h4 font-weight-bold mb-1">Email Verification</h1>
              <p class="text-subtitle-1 text-medium-emphasis">
                Verify your email address to activate your MCMD account
              </p>
            </div>

            <v-card-text class="pt-4">
              <!-- 验证中状态 -->
              <div v-if="verifying" class="text-center py-8">
                <v-progress-circular
                  indeterminate
                  color="primary"
                  size="64"
                  class="mb-4"
                ></v-progress-circular>
                <p class="text-h6 mb-2">Verifying your email...</p>
                <p class="text-body-2 text-medium-emphasis">
                  Please wait while we verify your email address.
                </p>
              </div>

              <!-- 验证成功 -->
              <div v-else-if="verificationResult && verificationResult.success" class="text-center py-8">
                <v-icon
                  color="success"
                  size="64"
                  class="mb-4"
                >
                  mdi-check-circle
                </v-icon>
                <h2 class="text-h5 mb-3 text-success">Email Verified Successfully!</h2>
                <p class="text-body-1 mb-4">
                  Welcome to MCMD, <strong>{{ verificationResult.username }}</strong>!
                </p>
                <p class="text-body-2 text-medium-emphasis mb-6">
                  Your account has been activated. You can now sign in and start using the Magnetocaloric Materials Database.
                </p>
                
                <v-btn
                  color="primary"
                  size="large"
                  variant="elevated"
                  @click="goToLogin"
                  class="mb-3"
                >
                  <v-icon left>mdi-login</v-icon>
                  Sign In Now
                </v-btn>
              </div>

              <!-- 验证失败 -->
              <div v-else-if="verificationResult && !verificationResult.success" class="text-center py-8">
                <v-icon
                  color="error"
                  size="64"
                  class="mb-4"
                >
                  mdi-alert-circle
                </v-icon>
                <h2 class="text-h5 mb-3 text-error">Verification Failed</h2>
                <p class="text-body-1 mb-4">
                  {{ verificationResult.message }}
                </p>
                
                <div class="d-flex flex-column gap-3">
                  <v-btn
                    color="primary"
                    variant="elevated"
                    @click="showResendForm = true"
                    v-if="!showResendForm"
                  >
                    <v-icon left>mdi-email-send</v-icon>
                    Request New Verification Email
                  </v-btn>
                  
                  <v-btn
                    color="grey"
                    variant="outlined"
                    @click="goToLogin"
                  >
                    <v-icon left>mdi-arrow-left</v-icon>
                    Back to Sign In
                  </v-btn>
                </div>
              </div>

              <!-- 重新发送验证邮件表单 -->
              <div v-if="showResendForm" class="mt-6">
                <v-divider class="mb-6"></v-divider>
                <h3 class="text-h6 mb-4">Request New Verification Email</h3>
                
                <v-form @submit.prevent="resendVerificationEmail" v-model="resendFormValid">
                  <v-text-field
                    v-model="resendEmail"
                    label="Email Address"
                    prepend-inner-icon="mdi-email"
                    variant="outlined"
                    :rules="emailRules"
                    required
                    density="comfortable"
                    bg-color="grey-lighten-5"
                    :loading="resending"
                  ></v-text-field>
                  
                  <div class="d-flex gap-3 mt-4">
                    <v-btn
                      type="submit"
                      color="primary"
                      variant="elevated"
                      :loading="resending"
                      :disabled="!resendFormValid"
                    >
                      <v-icon left>mdi-email-send</v-icon>
                      Send Verification Email
                    </v-btn>
                    
                    <v-btn
                      color="grey"
                      variant="outlined"
                      @click="showResendForm = false"
                    >
                      Cancel
                    </v-btn>
                  </div>
                </v-form>
              </div>

              <!-- 重新发送结果 -->
              <v-alert
                v-if="resendResult"
                :type="resendResult.success ? 'success' : 'error'"
                class="mt-4"
                variant="tonal"
              >
                {{ resendResult.message }}
              </v-alert>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'EmailVerification',
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const verifying = ref(false)
    const verificationResult = ref(null)
    const showResendForm = ref(false)
    const resendEmail = ref('')
    const resendFormValid = ref(false)
    const resending = ref(false)
    const resendResult = ref(null)
    
    const emailRules = [
      v => !!v || 'Email is required',
      v => /.+@.+\..+/.test(v) || 'Email must be valid'
    ]
    
    const verifyEmail = async (token) => {
      verifying.value = true
      try {
        const response = await axios.get(`/api/email-verification/verify?token=${token}`)
        verificationResult.value = response.data
      } catch (error) {
        verificationResult.value = {
          success: false,
          message: error.response?.data?.message || 'Verification failed. Please try again.'
        }
      } finally {
        verifying.value = false
      }
    }
    
    const resendVerificationEmail = async () => {
      resending.value = true
      resendResult.value = null
      
      try {
        const response = await axios.post('/api/email-verification/resend', {
          email: resendEmail.value
        })
        resendResult.value = response.data
        if (response.data.success) {
          showResendForm.value = false
          resendEmail.value = ''
        }
      } catch (error) {
        resendResult.value = {
          success: false,
          message: error.response?.data?.message || 'Failed to send verification email. Please try again.'
        }
      } finally {
        resending.value = false
      }
    }
    
    const goToLogin = () => {
      router.push('/login')
    }
    
    onMounted(() => {
      const token = route.query.token
      if (token) {
        verifyEmail(token)
      } else {
        verificationResult.value = {
          success: false,
          message: 'Invalid verification link. Please check your email for the correct link.'
        }
      }
    })
    
    return {
      verifying,
      verificationResult,
      showResendForm,
      resendEmail,
      resendFormValid,
      resending,
      resendResult,
      emailRules,
      resendVerificationEmail,
      goToLogin
    }
  }
}
</script>

<style scoped>
.verification-page {
  min-height: 100vh;
}

.verification-card {
  max-width: 500px;
  margin: 0 auto;
}

.animate-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.gap-3 {
  gap: 12px;
}
</style>
