<template>
  <v-container fluid class="manage-page pa-6">
    <div class="content-wrapper">
      <v-row>
        <v-col cols="12">
          <v-card class="mb-6">
            <v-card-title class="d-flex align-center py-4 px-6 bg-gradient">
              <v-icon color="white" class="mr-2" size="28">mdi-database-cog</v-icon>
              <span class="text-h5 font-weight-bold white--text">磁热材料数据库管理</span>
              <v-spacer></v-spacer>
              <!-- 用户信息显示 -->
              <div class="d-flex align-center">
                <v-chip
                  color="white"
                  variant="flat"
                  size="small"
                  prepend-icon="mdi-account"
                  :class="[
                    isAdmin ? 'mr-3' : '',
                    { 'text-black': !isAdmin }
                  ]"
                >
                  <span class="font-weight-medium">{{ currentUser || '未登录' }}</span>
                </v-chip>
                <!-- 只为管理员显示角色标识 -->
                <v-chip
                  v-if="isAdmin"
                  color="white"
                  variant="outlined"
                  size="small"
                  prepend-icon="mdi-shield-account"
                  class="text-white"
                  style="border-color: white;"
                >
                  管理员
                </v-chip>
              </div>
            </v-card-title>

            <!-- 操作按钮组 -->
            <v-card-text class="pa-6">
              <v-btn-group class="mb-6">
                <v-btn
                  v-for="op in operations"
                  :key="op.value"
                  v-show="canAccessOperation(op)"
                  :color="selectedOperation === op.value ? 'primary' : ''"
                  :variant="selectedOperation === op.value ? 'flat' : 'outlined'"
                  @click="setOperation(op.value)"
                  class="operation-btn"
                  :prepend-icon="op.icon"
                >
                  {{ op.text }}
                </v-btn>
              </v-btn-group>

              <!-- 游客用户权限提示 -->
              <v-alert
                v-if="userRole === 'guest'"
                type="info"
                variant="tonal"
                class="mb-4"
                prominent
              >
                <template v-slot:prepend>
                  <v-icon>mdi-information</v-icon>
                </template>
                <div>
                  <div class="text-h6 mb-2">游客用户权限说明</div>
                  <p class="mb-2">您当前是游客用户，只能查看数据库内容。如需添加、修改或删除数据，请联系管理员升级您的账户权限。</p>
                  <p class="mb-0">
                    <strong>游客权限：</strong>查看材料数据、搜索材料信息<br>
                    <strong>普通用户权限：</strong>添加新材料、修改/删除自己的材料<br>
                    <strong>管理员权限：</strong>管理所有材料、用户管理、数据导出
                  </p>
                </div>
              </v-alert>

              <!-- 文件上传按钮 -->
              <div v-if="selectedOperation === '新增'" class="mt-4">
                <!-- 第一排：上传按钮 -->
                <v-row class="mb-4">
                  <v-col cols="12">
                    <div class="d-flex flex-wrap gap-4 align-center">
                      <v-btn
                        color="primary"
                        prepend-icon="mdi-file-upload"
                        @click="$refs.fileInput.click()"
                        variant="outlined"
                      >
                        上传 JSON 文件
                      </v-btn>
                      <v-btn
                        color="primary"
                        prepend-icon="mdi-folder-zip"
                        @click="$refs.zipInput.click()"
                        variant="outlined"
                      >
                        批量上传 ZIP 文件
                      </v-btn>
                      <!-- JSON解析状态提示 -->
                      <v-chip
                        v-if="jsonParsed"
                        color="success"
                        variant="flat"
                        size="small"
                        prepend-icon="mdi-check-circle"
                      >
                        已解析：{{ jsonFileName }}
                      </v-chip>
                    </div>
                  </v-col>
                </v-row>

                <!-- 第二排：格式说明 -->
                <v-row class="mb-4">
                  <!-- JSON示例 -->
                  <v-col cols="12" md="6">
                    <v-card variant="outlined" class="json-example-card">
                      <v-card-title
                        class="py-3 px-4 d-flex align-center cursor-pointer"
                        @click="showJsonExample = !showJsonExample"
                      >
                        <v-icon color="info" class="mr-2">mdi-file-code</v-icon>
                        <span class="text-subtitle-2 font-weight-medium">JSON格式示例</span>
                        <v-spacer></v-spacer>
                        <v-btn
                          v-if="showJsonExample"
                          size="small"
                          variant="outlined"
                          color="primary"
                          @click.stop="copyJsonExample"
                          class="mr-2"
                        >
                          <v-icon size="small" class="mr-1">mdi-content-copy</v-icon>
                          复制
                        </v-btn>
                        <v-icon
                          :class="{ 'rotate-180': showJsonExample }"
                          class="transition-transform"
                        >
                          mdi-chevron-down
                        </v-icon>
                      </v-card-title>
                      <v-expand-transition>
                        <v-card-text v-show="showJsonExample" class="py-0 px-4 pb-4">
                          <div class="json-example-container">
                            <pre class="json-example-text">{{ jsonExample }}</pre>
                          </div>
                        </v-card-text>
                      </v-expand-transition>
                    </v-card>
                  </v-col>

                  <!-- ZIP文件结构说明 -->
                  <v-col cols="12" md="6">
                    <v-card variant="outlined" class="zip-structure-card">
                      <v-card-title
                        class="py-3 px-4 d-flex align-center cursor-pointer"
                        @click="showZipStructure = !showZipStructure"
                      >
                        <v-icon color="warning" class="mr-2">mdi-folder-zip</v-icon>
                        <span class="text-subtitle-2 font-weight-medium">ZIP文件结构规范</span>
                        <v-spacer></v-spacer>
                        <v-icon
                          :class="{ 'rotate-180': showZipStructure }"
                          class="transition-transform"
                        >
                          mdi-chevron-down
                        </v-icon>
                      </v-card-title>
                      <v-expand-transition>
                        <v-card-text v-show="showZipStructure" class="py-0 px-4 pb-4">
                          <div class="zip-structure-container">
                            <pre class="zip-structure-text">{{ zipStructureExample }}</pre>
                            <v-divider class="my-3"></v-divider>
                            <div class="text-caption text-grey-darken-1 pl-4">
                              <v-icon size="small" color="warning" class="mr-1">mdi-alert-circle</v-icon>
                              <strong>重要说明：</strong><br>
                              • 每个材料一个文件夹<br>
                              • 文件夹内必须包含 data.json 和 .cif 文件<br>
                              • CIF文件名应与JSON中的Formula字段匹配<br>
                              • 支持嵌套文件夹结构
                            </div>
                          </div>
                        </v-card-text>
                      </v-expand-transition>
                    </v-card>
                  </v-col>
                </v-row>

                <!-- 使用说明 -->
                <v-row class="mt-4">
                  <!-- 单个JSON文件上传说明 -->
                  <v-col cols="12" md="6">
                    <v-alert
                      type="info"
                      variant="tonal"
                      border="start"
                      icon="mdi-file-upload"
                    >
                      <div class="d-flex align-center mb-2">
                        <v-icon color="info" class="mr-2">mdi-file-code</v-icon>
                        <strong>单个JSON文件上传流程：</strong>
                      </div>
                      1. 首先上传CIF文件（必填项）<br>
                      2. 可选择上传JSON文件自动填充表单字段<br>
                      3. 手动填写或修改其他字段<br>
                      4. 最后点击提交按钮完成添加
                    </v-alert>
                  </v-col>

                  <!-- ZIP批量上传说明 -->
                  <v-col cols="12" md="6">
                    <v-alert
                      type="warning"
                      variant="tonal"
                      border="start"
                      icon="mdi-folder-zip"
                    >
                      <div class="d-flex align-center mb-2">
                        <v-icon color="warning" class="mr-2">mdi-package-variant</v-icon>
                        <strong>ZIP批量上传要求：</strong>
                      </div>
                      1. ZIP文件内每个材料一个文件夹<br>
                      2. 每个文件夹必须包含data.json和.cif文件<br>
                      3. CIF文件名需与JSON中Formula字段匹配<br>
                      4. 系统将自动批量处理所有材料
                    </v-alert>
                  </v-col>
                </v-row>

                <input
                  type="file"
                  ref="fileInput"
                  @change="handleFileUpload"
                  accept=".json"
                  style="display: none"
                >
                <input
                  type="file"
                  ref="zipInput"
                  @change="handleZipUpload"
                  accept=".zip"
                  style="display: none"
                >
              </div>

              <!-- 删除全部按钮（仅管理员可见） -->
              <div v-if="selectedOperation === '删除' && isAdmin" class="mt-4">
                <v-alert
                  type="warning"
                  variant="tonal"
                  class="mb-4"
                  border="start"
                  icon="mdi-shield-account"
                >
                  <div class="d-flex align-center">
                    <div class="flex-grow-1">
                      <strong>管理员功能</strong><br>
                      <span class="text-body-2">您可以删除数据库中的所有材料数据</span>
                    </div>
                    <v-btn
                      color="error"
                      prepend-icon="mdi-delete-sweep"
                      @click="confirmDeleteAll"
                      variant="outlined"
                      size="small"
                    >
                      删除全部数据
                    </v-btn>
                  </div>
                </v-alert>
              </div>

              <!-- 数据导出功能（仅管理员可见） -->
              <div v-if="selectedOperation === '数据导出' && isAdmin" class="mt-4">
                <v-alert
                  type="info"
                  variant="tonal"
                  class="mb-4"
                  border="start"
                  icon="mdi-shield-account"
                >
                  <div class="d-flex align-center">
                    <div class="flex-grow-1">
                      <strong>管理员功能 - 数据导出</strong><br>
                      <span class="text-body-2">导出数据库中的所有材料数据和对应的CIF文件</span>
                    </div>
                    <v-btn
                      color="primary"
                      prepend-icon="mdi-download"
                      @click="exportAllData"
                      variant="outlined"
                      size="small"
                      :loading="exportLoading"
                      :disabled="exportLoading"
                    >
                      {{ exportLoading ? '导出中...' : '下载全部数据' }}
                    </v-btn>
                  </div>
                </v-alert>

                <!-- 导出说明 -->
                <v-card variant="outlined" class="mb-4">
                  <v-card-title class="py-3 px-4 d-flex align-center">
                    <v-icon color="info" class="mr-2">mdi-information</v-icon>
                    <span class="text-subtitle-1 font-weight-medium">导出内容说明</span>
                  </v-card-title>
                  <v-card-text class="py-3 px-4">
                    <v-list density="compact">
                      <v-list-item prepend-icon="mdi-folder">
                        <v-list-item-title>文件夹结构</v-list-item-title>
                        <v-list-item-subtitle>每个材料一个文件夹，与批量导入格式一致</v-list-item-subtitle>
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-file-code">
                        <v-list-item-title>data.json</v-list-item-title>
                        <v-list-item-subtitle>每个文件夹内包含材料的完整属性信息</v-list-item-subtitle>
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-file-document">
                        <v-list-item-title>CIF结构文件</v-list-item-title>
                        <v-list-item-subtitle>对应的晶体结构文件（如果存在）</v-list-item-subtitle>
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-information-outline">
                        <v-list-item-title>导出信息文件</v-list-item-title>
                        <v-list-item-subtitle>包含导出时间、统计信息等元数据</v-list-item-subtitle>
                      </v-list-item>
                      <v-list-item prepend-icon="mdi-folder-zip">
                        <v-list-item-title>文件格式</v-list-item-title>
                        <v-list-item-subtitle>ZIP压缩包，可直接用于批量导入</v-list-item-subtitle>
                      </v-list-item>
                    </v-list>

                    <!-- 文件结构示例 -->
                    <v-divider class="my-3"></v-divider>
                    <div class="text-caption text-grey-darken-1 mb-2">
                      <v-icon size="small" color="info" class="mr-1">mdi-file-tree</v-icon>
                      <strong>导出文件结构示例：</strong>
                    </div>
                    <div class="zip-structure-container">
                      <pre class="zip-structure-text">MCMD_Export_20250120_143022.zip
├── export_info.json       # 导出信息
├── material1/
│   ├── data.json         # 材料数据
│   └── Fe2O3.cif        # CIF文件
├── material2/
│   ├── data.json
│   └── NaCl.cif
└── material3/
    ├── data.json
    └── SiO2.cif</pre>
                    </div>
                  </v-card-text>
                </v-card>

                <!-- 导出进度 -->
                <v-card v-if="exportLoading" variant="outlined" class="mb-4">
                  <v-card-text class="pa-4">
                    <div class="d-flex align-center">
                      <v-progress-circular
                        indeterminate
                        color="primary"
                        size="24"
                        class="mr-3"
                      ></v-progress-circular>
                      <div>
                        <div class="text-subtitle-2">正在导出数据...</div>
                        <div class="text-caption text-grey">请稍候，正在生成包含所有材料数据的压缩包</div>
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
          </v-card>

          <!-- 上传进度条 -->
          <v-card v-if="isUploading" class="mb-6">
            <v-card-text class="pa-6">
              <v-progress-linear
                v-model="uploadProgress"
                color="primary"
                height="20"
                striped
              >
                <template v-slot:default="{ value }">
                  <strong>{{ Math.ceil(value) }}%</strong>
                </template>
              </v-progress-linear>
              <div class="text-center mt-2">
                <span class="text-subtitle-1">{{ processedFiles }}/{{ totalFiles }} 文件已处理</span>
              </div>
              <div v-if="currentProcessingFile" class="text-center mt-1">
                <span class="text-caption text-grey">正在处理：{{ currentProcessingFile }}</span>
              </div>
            </v-card-text>
          </v-card>

          <!-- 批量上传结果 -->
          <v-card v-if="batchUploadResult" class="mb-6">
            <v-card-title class="py-4 px-6 d-flex align-center">
              <v-icon :color="batchUploadResult.hasErrors ? 'warning' : 'success'" class="mr-2">
                {{ batchUploadResult.hasErrors ? 'mdi-alert-circle' : 'mdi-check-circle' }}
              </v-icon>
              <span class="text-h6">批量上传结果</span>
            </v-card-title>
            <v-card-text class="pa-6">
              <!-- 统计信息 -->
              <v-row class="mb-4">
                <v-col cols="12" sm="4">
                  <v-card variant="outlined" class="text-center pa-4">
                    <v-icon color="success" size="large" class="mb-2">mdi-check-circle</v-icon>
                    <div class="text-h4 text-success">{{ batchUploadResult.successCount }}</div>
                    <div class="text-caption">成功上传</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="4">
                  <v-card variant="outlined" class="text-center pa-4">
                    <v-icon color="error" size="large" class="mb-2">mdi-close-circle</v-icon>
                    <div class="text-h4 text-error">{{ batchUploadResult.failureCount }}</div>
                    <div class="text-caption">上传失败</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="4">
                  <v-card variant="outlined" class="text-center pa-4">
                    <v-icon color="info" size="large" class="mb-2">mdi-file-multiple</v-icon>
                    <div class="text-h4 text-info">{{ batchUploadResult.totalCount }}</div>
                    <div class="text-caption">总计文件</div>
                  </v-card>
                </v-col>
              </v-row>

              <!-- 失败详情 -->
              <div v-if="batchUploadResult.failures.length > 0">
                <v-divider class="mb-4"></v-divider>
                <h4 class="text-h6 mb-3 text-error">
                  <v-icon class="mr-2">mdi-alert-circle</v-icon>
                  失败详情
                </h4>
                <v-expansion-panels variant="accordion">
                  <v-expansion-panel
                    v-for="(failure, index) in batchUploadResult.failures"
                    :key="index"
                  >
                    <v-expansion-panel-title>
                      <v-icon color="error" class="mr-2">mdi-file-alert</v-icon>
                      {{ failure.fileName }}
                      <v-spacer></v-spacer>
                      <v-chip size="small" color="error" variant="outlined">
                        {{ failure.errorType }}
                      </v-chip>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="text-body-2">
                        <strong>错误原因：</strong>{{ failure.errorMessage }}<br>
                        <strong>文件路径：</strong>{{ failure.filePath }}<br>
                        <span v-if="failure.suggestion">
                          <strong>建议：</strong>{{ failure.suggestion }}
                        </span>
                      </div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>

              <!-- 成功列表 -->
              <div v-if="batchUploadResult.successes.length > 0">
                <v-divider class="mb-4"></v-divider>
                <h4 class="text-h6 mb-3 text-success">
                  <v-icon class="mr-2">mdi-check-circle</v-icon>
                  成功上传 ({{ batchUploadResult.successes.length }})
                </h4>
                <v-chip-group column>
                  <v-chip
                    v-for="success in batchUploadResult.successes"
                    :key="success.id"
                    color="success"
                    variant="outlined"
                    size="small"
                  >
                    <v-icon size="small" class="mr-1">mdi-check</v-icon>
                    {{ success.id }} ({{ success.formula }})
                  </v-chip>
                </v-chip-group>
              </div>

              <v-btn
                color="primary"
                variant="outlined"
                class="mt-4"
                @click="batchUploadResult = null"
              >
                关闭结果
              </v-btn>
            </v-card-text>
          </v-card>

          <!-- 新增表单 -->
          <v-card v-if="selectedOperation === '新增'" class="mb-6">
            <v-card-title class="py-4 px-6 d-flex align-center">
              <v-icon color="primary" class="mr-2">mdi-database-plus</v-icon>
              <span class="text-h6">新增数据</span>
            </v-card-title>
            <v-card-text class="pa-6">
              <v-form @submit.prevent="submitData">
                <!-- CIF文件上传区域（置顶） -->
                <v-card class="mb-4 field-group-card" variant="outlined">
                  <v-card-title class="py-3 px-4 d-flex align-center group-title">
                    <v-icon icon="mdi-file-upload" color="error" class="mr-2"></v-icon>
                    <span class="text-subtitle-1 font-weight-medium">CIF文件上传</span>
                    <v-spacer></v-spacer>
                    <v-chip size="small" color="error" variant="flat">必填</v-chip>
                  </v-card-title>
                  <v-card-text class="py-3 px-4">
                    <v-file-input
                      v-model="cifFile"
                      label="选择CIF文件"
                      accept=".cif"
                      variant="outlined"
                      density="comfortable"
                      prepend-icon="mdi-file-document"
                      show-size
                      clearable
                      hide-details="auto"
                      :rules="cifFileRules"
                    >
                      <template v-slot:selection="{ fileNames }">
                        <v-chip
                          v-for="fileName in fileNames"
                          :key="fileName"
                          color="primary"
                          size="small"
                          label
                          class="me-2"
                        >
                          {{ fileName }}
                        </v-chip>
                      </template>
                    </v-file-input>
                    <div class="text-caption text-grey mt-2">
                      <v-icon size="small" class="mr-1" color="error">mdi-asterisk</v-icon>
                      <span class="text-error font-weight-medium">必填项：</span>支持.cif格式文件，文件大小不超过10MB，将重命名为{Formula}.cif上传到COS
                    </div>
                  </v-card-text>
                </v-card>

                <!-- 分组显示表单字段 -->
                <div>
                  <v-card
                    v-for="group in fieldGroups"
                    :key="group.title"
                    class="mb-4 field-group-card"
                    variant="outlined"
                  >
                    <v-card-title class="py-3 px-4 d-flex align-center group-title">
                      <v-icon :icon="group.icon" color="primary" class="mr-2"></v-icon>
                      <span class="text-subtitle-1 font-weight-medium">{{ group.title }}</span>
                      <!-- 自定义属性组的添加按钮 -->
                      <v-spacer v-if="group.isCustom"></v-spacer>
                      <v-btn
                        v-if="group.isCustom"
                        size="small"
                        color="primary"
                        variant="outlined"
                        prepend-icon="mdi-plus"
                        @click="openCustomPropertyDialog"
                      >
                        添加属性
                      </v-btn>
                    </v-card-title>
                    <v-card-text class="py-3 px-4">
                      <!-- 常规字段 -->
                      <v-row v-if="!group.isCustom">
                        <v-col
                          v-for="field in group.fields"
                          :key="field"
                          cols="12"
                          sm="6"
                          md="4"
                        >
                          <v-text-field
                            v-if="field !== 'Path' && field !== 'DOI'"
                            v-model="formData[field]"
                            :label="`${field} (${getFieldLabel(field)})`"
                            :type="getFieldType(field)"
                            variant="outlined"
                            density="comfortable"
                            :rules="isRequired(field) ? [v => !!v || `${getFieldLabel(field)}不能为空`] : []"
                            hide-details="auto"
                          ></v-text-field>
                          <v-textarea
                            v-else-if="field === 'Path'"
                            v-model="formData[field]"
                            :label="`${field} (${getFieldLabel(field)})`"
                            variant="outlined"
                            auto-grow
                            rows="4"
                            hide-details="auto"
                          ></v-textarea>
                          <v-text-field
                            v-else-if="field === 'DOI'"
                            v-model="formData[field]"
                            :label="`${field} (${getFieldLabel(field)})`"
                            variant="outlined"
                            density="comfortable"
                            hide-details="auto"
                            placeholder="10.xxxx/xxxxx"
                          ></v-text-field>
                        </v-col>
                      </v-row>

                      <!-- 自定义属性字段 -->
                      <div v-else-if="group.isCustom">
                        <div v-if="formData.customProperties.length === 0" class="text-center py-8">
                          <v-icon size="64" color="grey-lighten-1">mdi-plus-circle-outline</v-icon>
                          <p class="text-body-1 text-grey mt-4">暂无自定义属性</p>
                          <p class="text-body-2 text-grey">点击"添加属性"按钮添加自定义属性</p>
                        </div>
                        <v-row v-else>
                          <v-col
                            v-for="(property, index) in formData.customProperties"
                            :key="property.id"
                            cols="12"
                            sm="6"
                            md="4"
                          >
                            <v-text-field
                              v-if="property.type === 'string'"
                              v-model="property.value"
                              :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                              variant="outlined"
                              density="comfortable"
                              hide-details="auto"
                              :hint="property.description"
                            >
                              <template v-slot:append>
                                <v-btn
                                  icon="mdi-pencil"
                                  size="x-small"
                                  variant="text"
                                  @click="editCustomProperty(index)"
                                ></v-btn>
                                <v-btn
                                  icon="mdi-delete"
                                  size="x-small"
                                  variant="text"
                                  color="error"
                                  @click="removeCustomProperty(index)"
                                ></v-btn>
                              </template>
                            </v-text-field>
                            <v-text-field
                              v-else-if="property.type === 'number'"
                              v-model.number="property.value"
                              :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                              type="number"
                              variant="outlined"
                              density="comfortable"
                              hide-details="auto"
                              :hint="property.description"
                            >
                              <template v-slot:append>
                                <v-btn
                                  icon="mdi-pencil"
                                  size="x-small"
                                  variant="text"
                                  @click="editCustomProperty(index)"
                                ></v-btn>
                                <v-btn
                                  icon="mdi-delete"
                                  size="x-small"
                                  variant="text"
                                  color="error"
                                  @click="removeCustomProperty(index)"
                                ></v-btn>
                              </template>
                            </v-text-field>
                            <v-switch
                              v-else-if="property.type === 'boolean'"
                              v-model="property.value"
                              :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                              color="primary"
                              hide-details="auto"
                              :hint="property.description"
                            >
                              <template v-slot:append>
                                <v-btn
                                  icon="mdi-pencil"
                                  size="x-small"
                                  variant="text"
                                  @click="editCustomProperty(index)"
                                ></v-btn>
                                <v-btn
                                  icon="mdi-delete"
                                  size="x-small"
                                  variant="text"
                                  color="error"
                                  @click="removeCustomProperty(index)"
                                ></v-btn>
                              </template>
                            </v-switch>
                          </v-col>
                        </v-row>
                      </div>
                    </v-card-text>
                  </v-card>
                </div>

                <v-alert
                  v-if="error"
                  type="error"
                  variant="tonal"
                  class="mt-4"
                  closable
                  @click:close="error = ''"
                >
                  {{ error }}
                </v-alert>

                <v-btn
                  type="submit"
                  color="primary"
                  class="mt-6"
                  :loading="submitting"
                  block
                >
                  提交
                </v-btn>
              </v-form>
            </v-card-text>
          </v-card>

          <!-- 修改表单 -->
          <template v-if="selectedOperation === '修改'">
            <v-card v-if="!materialToModify" class="mb-6">
              <v-card-title class="py-4 px-6 d-flex align-center">
                <v-icon color="primary" class="mr-2">mdi-database-search</v-icon>
                <span class="text-h6">查询要修改的数据</span>
              </v-card-title>
              <v-card-text class="pa-6">
                <v-form @submit.prevent="fetchMaterialById(selectedMaterialId, 'modify')">
                  <v-text-field
                    v-model="selectedMaterialId"
                    label="输入要修改的数据的ID"
                    prefix="MCMD-"
                    variant="outlined"
                    density="comfortable"
                    hide-details="auto"
                    class="mb-4"
                    placeholder="例如：001"
                    :rules="[v => !!v || 'ID不能为空']"
                  ></v-text-field>
                  <v-btn
                    type="submit"
                    color="primary"
                    :loading="loading"
                    block
                  >
                    查询
                  </v-btn>
                </v-form>
              </v-card-text>
            </v-card>

            <v-card v-else class="mb-6">
              <v-card-title class="py-4 px-6 d-flex align-center">
                <v-icon color="primary" class="mr-2">mdi-database-edit</v-icon>
                <span class="text-h6">修改数据</span>
                <v-chip class="ml-4" color="primary" variant="outlined">
                  ID: MCMD-{{ selectedMaterialId }}
                </v-chip>
              </v-card-title>
              <v-card-text class="pa-6">
                <!-- 数据贡献者信息 -->
                <v-card class="mb-4" variant="outlined" v-if="materialToModify">
                  <v-card-title class="py-3 px-4 d-flex align-center bg-info">
                    <v-icon color="white" class="mr-2">mdi-account-circle</v-icon>
                    <span class="text-subtitle-1 font-weight-medium white--text">数据贡献者信息</span>
                  </v-card-title>
                  <v-card-text class="py-3 px-4">
                    <v-row>
                      <v-col cols="12" sm="6">
                        <div class="d-flex align-center mb-2">
                          <v-icon color="success" class="mr-2">mdi-account</v-icon>
                          <span class="text-body-2 text-grey-darken-1">贡献者：</span>
                          <v-chip
                            color="success"
                            variant="outlined"
                            size="small"
                            class="ml-2"
                          >
                            {{ materialToModify.createdBy || 'Unknown' }}
                          </v-chip>
                        </div>
                        <div class="d-flex align-center">
                          <v-icon color="primary" class="mr-2">mdi-calendar</v-icon>
                          <span class="text-body-2 text-grey-darken-1">创建时间：</span>
                          <span class="ml-2 text-body-2">{{ formatDate(materialToModify.createdAt) }}</span>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" v-if="materialToModify.lastModifiedBy && materialToModify.lastModifiedBy !== materialToModify.createdBy">
                        <div class="d-flex align-center mb-2">
                          <v-icon color="warning" class="mr-2">mdi-account-edit</v-icon>
                          <span class="text-body-2 text-grey-darken-1">最后修改者：</span>
                          <v-chip
                            color="warning"
                            variant="outlined"
                            size="small"
                            class="ml-2"
                          >
                            {{ materialToModify.lastModifiedBy }}
                          </v-chip>
                        </div>
                        <div class="d-flex align-center">
                          <v-icon color="primary" class="mr-2">mdi-calendar-edit</v-icon>
                          <span class="text-body-2 text-grey-darken-1">修改时间：</span>
                          <span class="ml-2 text-body-2">{{ formatDate(materialToModify.lastModifiedAt) }}</span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>

                <!-- 权限检查提示 -->
                <v-alert
                  v-if="materialToModify && !canModifyMaterial"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  border="start"
                  icon="mdi-lock"
                >
                  您没有权限修改此数据。只有数据贡献者或管理员可以修改数据。
                </v-alert>

                <v-form @submit.prevent="updateData" v-model="isValid">
                  <!-- CIF文件上传区域（修改时） -->
                  <v-card class="mb-4 field-group-card" variant="outlined">
                    <v-card-title class="py-3 px-4 d-flex align-center group-title">
                      <v-icon icon="mdi-file-upload" color="primary" class="mr-2"></v-icon>
                      <span class="text-subtitle-1 font-weight-medium">CIF文件更新</span>
                      <v-spacer></v-spacer>
                      <v-chip size="small" color="info" variant="outlined">可选</v-chip>
                    </v-card-title>
                    <v-card-text class="py-3 px-4">
                      <v-file-input
                        v-model="cifFile"
                        label="选择新的CIF文件（留空则不更新）"
                        accept=".cif"
                        variant="outlined"
                        density="comfortable"
                        prepend-icon="mdi-file-document"
                        show-size
                        clearable
                        hide-details="auto"
                        :rules="cifFileRules"
                      >
                        <template v-slot:selection="{ fileNames }">
                          <v-chip
                            v-for="fileName in fileNames"
                            :key="fileName"
                            color="primary"
                            size="small"
                            label
                            class="me-2"
                          >
                            {{ fileName }}
                          </v-chip>
                        </template>
                      </v-file-input>
                      <div class="text-caption text-grey mt-2">
                        <v-icon size="small" class="mr-1">mdi-information</v-icon>
                        支持.cif格式文件，文件大小不超过10MB。如果不选择文件，将保持原有CIF文件不变。
                      </div>
                    </v-card-text>
                  </v-card>

                  <!-- 分组显示表单字段 -->
                  <div>
                    <v-card
                      v-for="group in fieldGroups"
                      :key="group.title"
                      class="mb-4 field-group-card"
                      variant="outlined"
                    >
                      <v-card-title class="py-3 px-4 d-flex align-center group-title">
                        <v-icon :icon="group.icon" color="primary" class="mr-2"></v-icon>
                        <span class="text-subtitle-1 font-weight-medium">{{ group.title }}</span>
                        <!-- 自定义属性组的添加按钮 -->
                        <v-spacer v-if="group.isCustom"></v-spacer>
                        <v-btn
                          v-if="group.isCustom"
                          size="small"
                          color="primary"
                          variant="outlined"
                          prepend-icon="mdi-plus"
                          @click="openCustomPropertyDialog"
                        >
                          添加属性
                        </v-btn>
                      </v-card-title>
                      <v-card-text class="py-3 px-4">
                        <!-- 常规字段 -->
                        <v-row v-if="!group.isCustom">
                          <v-col
                            v-for="field in group.fields.filter(f => f !== 'id')"
                            :key="field"
                            cols="12"
                            sm="6"
                            md="4"
                          >
                            <v-text-field
                              v-if="field !== 'Path' && field !== 'DOI'"
                              v-model="formData[field]"
                              :label="`${field} (${getFieldLabel(field)})`"
                              :type="getFieldType(field)"
                              variant="outlined"
                              density="comfortable"
                              :rules="isRequired(field) ? [v => !!v || `${getFieldLabel(field)}不能为空`] : []"
                              hide-details="auto"
                            ></v-text-field>
                            <v-textarea
                              v-else-if="field === 'Path'"
                              v-model="formData[field]"
                              :label="`${field} (${getFieldLabel(field)})`"
                              variant="outlined"
                              auto-grow
                              rows="4"
                              hide-details="auto"
                            ></v-textarea>
                            <v-text-field
                              v-else-if="field === 'DOI'"
                              v-model="formData[field]"
                              :label="`${field} (${getFieldLabel(field)})`"
                              variant="outlined"
                              density="comfortable"
                              hide-details="auto"
                              placeholder="10.xxxx/xxxxx"
                            ></v-text-field>
                          </v-col>
                        </v-row>

                        <!-- 自定义属性字段 -->
                        <div v-else-if="group.isCustom">
                          <div v-if="formData.customProperties.length === 0" class="text-center py-8">
                            <v-icon size="64" color="grey-lighten-1">mdi-plus-circle-outline</v-icon>
                            <p class="text-body-1 text-grey mt-4">暂无自定义属性</p>
                            <p class="text-body-2 text-grey">点击"添加属性"按钮添加自定义属性</p>
                          </div>
                          <v-row v-else>
                            <v-col
                              v-for="(property, index) in formData.customProperties"
                              :key="property.id"
                              cols="12"
                              sm="6"
                              md="4"
                            >
                              <v-text-field
                                v-if="property.type === 'string'"
                                v-model="property.value"
                                :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                                variant="outlined"
                                density="comfortable"
                                hide-details="auto"
                                :hint="property.description"
                              >
                                <template v-slot:append>
                                  <v-btn
                                    icon="mdi-pencil"
                                    size="x-small"
                                    variant="text"
                                    @click="editCustomProperty(index)"
                                  ></v-btn>
                                  <v-btn
                                    icon="mdi-delete"
                                    size="x-small"
                                    variant="text"
                                    color="error"
                                    @click="removeCustomProperty(index)"
                                  ></v-btn>
                                </template>
                              </v-text-field>
                              <v-text-field
                                v-else-if="property.type === 'number'"
                                v-model.number="property.value"
                                :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                                type="number"
                                variant="outlined"
                                density="comfortable"
                                hide-details="auto"
                                :hint="property.description"
                              >
                                <template v-slot:append>
                                  <v-btn
                                    icon="mdi-pencil"
                                    size="x-small"
                                    variant="text"
                                    @click="editCustomProperty(index)"
                                  ></v-btn>
                                  <v-btn
                                    icon="mdi-delete"
                                    size="x-small"
                                    variant="text"
                                    color="error"
                                    @click="removeCustomProperty(index)"
                                  ></v-btn>
                                </template>
                              </v-text-field>
                              <v-switch
                                v-else-if="property.type === 'boolean'"
                                v-model="property.value"
                                :label="`${property.key}${property.unit ? ' (' + property.unit + ')' : ''}`"
                                color="primary"
                                hide-details="auto"
                                :hint="property.description"
                              >
                                <template v-slot:append>
                                  <v-btn
                                    icon="mdi-pencil"
                                    size="x-small"
                                    variant="text"
                                    @click="editCustomProperty(index)"
                                  ></v-btn>
                                  <v-btn
                                    icon="mdi-delete"
                                    size="x-small"
                                    variant="text"
                                    color="error"
                                    @click="removeCustomProperty(index)"
                                  ></v-btn>
                                </template>
                              </v-switch>
                            </v-col>
                          </v-row>
                        </div>
                      </v-card-text>
                    </v-card>
                  </div>

                  <v-btn
                    type="submit"
                    color="primary"
                    class="mt-6"
                    :loading="submitting"
                    :disabled="submitting || !canModifyMaterial"
                    block
                  >
                    提交修改
                  </v-btn>
                </v-form>
              </v-card-text>
            </v-card>
          </template>

          <!-- 我的材料 -->
          <template v-if="selectedOperation === '我的材料'">
            <v-card class="mb-6">
              <v-card-title class="py-4 px-6 d-flex align-center">
                <v-icon color="success" class="mr-2">mdi-account-box</v-icon>
                <span class="text-h6">我的材料</span>
                <v-spacer></v-spacer>
                <!-- 只为管理员显示角色标识 -->
                <v-chip
                  v-if="isAdmin"
                  color="orange"
                  variant="outlined"
                  size="small"
                  prepend-icon="mdi-shield-account"
                >
                  管理员
                </v-chip>
              </v-card-title>
              <v-card-text class="pa-6">
                <!-- 加载状态 -->
                <div v-if="myMaterialsLoading" class="text-center py-8">
                  <v-progress-circular
                    indeterminate
                    color="success"
                    size="64"
                  ></v-progress-circular>
                  <p class="text-body-1 mt-4">正在加载我的材料...</p>
                </div>

                <!-- 材料列表 -->
                <div v-else-if="myMaterialsResults.length > 0">
                  <h3 class="text-h6 mb-4 text-success">
                    <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>
                    我的材料 ({{ myMaterialsResults.length }} 个)
                  </h3>
                  <v-row>
                    <v-col
                      v-for="material in myMaterialsResults"
                      :key="material.id"
                      cols="12"
                      sm="6"
                      md="4"
                    >
                      <v-card
                        class="material-card"

                      >
                        <div @click="navigateToMaterial(material.id)" style="cursor: pointer;">
                          <v-card-title class="text-subtitle-1 pa-3">
                            <v-icon class="mr-2" color="success">mdi-atom</v-icon>
                            <span v-html="formatFormula(material.Formula)"></span>
                          </v-card-title>
                          <v-card-subtitle class="px-3 pb-2">
                            ID: {{ material.id }}
                          </v-card-subtitle>
                          <v-card-text class="pt-0 px-3 pb-2">
                            <v-chip
                              size="small"
                              color="success"
                              variant="outlined"
                              prepend-icon="mdi-account"
                            >
                              我的材料
                            </v-chip>
                          </v-card-text>
                        </div>

                        <!-- 操作按钮 -->
                        <v-card-actions class="px-3 pb-3 pt-0">
                          <v-btn
                            size="small"
                            color="primary"
                            variant="outlined"
                            prepend-icon="mdi-pencil"
                            @click="editMaterial(material)"
                            class="mr-2"
                          >
                            修改
                          </v-btn>
                          <v-btn
                            size="small"
                            color="error"
                            variant="outlined"
                            prepend-icon="mdi-delete"
                            @click="confirmDeleteMaterial(material)"
                          >
                            删除
                          </v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>

                <!-- 无材料提示 -->
                <div v-else-if="myMaterialsSearched && !myMaterialsLoading" class="text-center py-8">
                  <v-icon size="64" color="grey">mdi-folder-open-outline</v-icon>
                  <p class="text-h6 grey--text mt-4">您还没有创建任何材料</p>
                  <p class="text-body-2 grey--text">点击"新增数据"开始添加您的第一个材料</p>
                </div>
              </v-card-text>
            </v-card>
          </template>

          <!-- 删除表单 -->
          <template v-if="selectedOperation === '删除'">
            <v-card v-if="!materialToDelete" class="mb-6">
              <v-card-title class="py-4 px-6 d-flex align-center">
                <v-icon color="error" class="mr-2">mdi-database-search</v-icon>
                <span class="text-h6">删除数据</span>
              </v-card-title>
              <v-card-text class="pa-6">
                <v-form @submit.prevent="fetchMaterialById(selectedMaterialId, 'delete')">
                  <v-text-field
                    v-model="selectedMaterialId"
                    label="输入要删除的数据的ID"
                    prefix="MCMD-"
                    variant="outlined"
                    density="comfortable"
                    hide-details="auto"
                    class="mb-4"
                    placeholder="例如：001"
                    :rules="[v => !!v || 'ID不能为空']"
                  ></v-text-field>
                  <v-btn
                    type="submit"
                    color="error"
                    :loading="loading"
                    block
                  >
                    查询
                  </v-btn>
                </v-form>
              </v-card-text>
            </v-card>

            <v-card v-else class="mb-6">
              <v-card-title class="py-4 px-6 d-flex align-center">
                <v-icon color="error" class="mr-2">mdi-database-remove</v-icon>
                <span class="text-h6">确认删除以下数据</span>
                <v-chip class="ml-4" color="error" variant="outlined">
                  ID: MCMD-{{ selectedMaterialId }}
                </v-chip>
              </v-card-title>
              <v-card-text class="pa-6">
                <!-- 数据贡献者信息 -->
                <v-card class="mb-4" variant="outlined">
                  <v-card-title class="py-3 px-4 d-flex align-center bg-info">
                    <v-icon color="white" class="mr-2">mdi-account-circle</v-icon>
                    <span class="text-subtitle-1 font-weight-medium white--text">数据贡献者信息</span>
                  </v-card-title>
                  <v-card-text class="py-3 px-4">
                    <v-row>
                      <v-col cols="12" sm="6">
                        <div class="d-flex align-center mb-2">
                          <v-icon color="success" class="mr-2">mdi-account</v-icon>
                          <span class="text-body-2 text-grey-darken-1">贡献者：</span>
                          <v-chip
                            color="success"
                            variant="outlined"
                            size="small"
                            class="ml-2"
                          >
                            {{ materialToDelete.createdBy || 'Unknown' }}
                          </v-chip>
                        </div>
                        <div class="d-flex align-center">
                          <v-icon color="primary" class="mr-2">mdi-calendar</v-icon>
                          <span class="text-body-2 text-grey-darken-1">创建时间：</span>
                          <span class="ml-2 text-body-2">{{ formatDate(materialToDelete.createdAt) }}</span>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" v-if="materialToDelete.lastModifiedBy && materialToDelete.lastModifiedBy !== materialToDelete.createdBy">
                        <div class="d-flex align-center mb-2">
                          <v-icon color="warning" class="mr-2">mdi-account-edit</v-icon>
                          <span class="text-body-2 text-grey-darken-1">最后修改者：</span>
                          <v-chip
                            color="warning"
                            variant="outlined"
                            size="small"
                            class="ml-2"
                          >
                            {{ materialToDelete.lastModifiedBy }}
                          </v-chip>
                        </div>
                        <div class="d-flex align-center">
                          <v-icon color="primary" class="mr-2">mdi-calendar-edit</v-icon>
                          <span class="text-body-2 text-grey-darken-1">修改时间：</span>
                          <span class="ml-2 text-body-2">{{ formatDate(materialToDelete.lastModifiedAt) }}</span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>

                <!-- 权限检查提示 -->
                <v-alert
                  v-if="!canDeleteMaterial"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                  border="start"
                  icon="mdi-lock"
                >
                  您没有权限删除此数据。只有数据贡献者或管理员可以删除数据。
                </v-alert>

                <v-alert
                  v-else
                  type="warning"
                  variant="tonal"
                  class="mb-6"
                  border="start"
                  icon="mdi-alert"
                >
                  此操作不可撤销，请确认是否删除该数据。
                </v-alert>

                <v-card class="material-data pa-4" variant="outlined">
                  <pre>{{ JSON.stringify(materialToDelete, null, 2) }}</pre>
                </v-card>
                <div class="d-flex gap-4 mt-6">
                  <v-btn
                    variant="outlined"
                    @click="cancelDeletion"
                    :disabled="submitting"
                    width="140"
                  >
                    取消
                  </v-btn>
                  <v-btn
                    color="error"
                    @click="deleteData"
                    :loading="submitting"
                    :disabled="!canDeleteMaterial || submitting"
                    width="140"
                  >
                    确认删除
                  </v-btn>
                </div>
              </v-card-text>
            </v-card>
          </template>
        </v-col>
      </v-row>
    </div>

    <!-- 确认删除全部对话框（仅管理员） -->
    <v-dialog v-model="showDeleteAllDialog" max-width="500">
      <v-card>
        <v-card-title class="text-h5 pa-6 d-flex align-center">
          <v-icon color="error" class="mr-2">mdi-alert-circle</v-icon>
          确认删除全部数据
          <v-spacer></v-spacer>
          <v-chip color="red-darken-1" variant="outlined" size="small" prepend-icon="mdi-shield-account">
            管理员操作
          </v-chip>
        </v-card-title>
        <v-card-text class="pa-6">
          <v-alert
            type="error"
            variant="tonal"
            border="start"
            icon="mdi-alert"
            class="mb-4"
          >
            <strong>危险操作警告</strong><br>
            此操作将删除数据库中的所有材料数据，且不可撤销。请确认是否继续？
          </v-alert>
          <v-alert
            type="info"
            variant="tonal"
            border="start"
            icon="mdi-information"
          >
            <strong>操作说明</strong><br>
            • 将删除所有材料的数据库记录<br>
            • 将删除所有关联的CIF文件<br>
            • 此操作仅限管理员执行
          </v-alert>
        </v-card-text>
        <v-card-actions class="pa-6">
          <v-spacer></v-spacer>
          <v-btn
            variant="outlined"
            @click="showDeleteAllDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            color="error"
            @click="deleteAllMaterials"
            :loading="submitting"
            class="ml-4"
          >
            确认删除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 删除单个材料确认对话框 -->
    <v-dialog v-model="showDeleteConfirmDialog" max-width="500">
      <v-card>
        <v-card-title class="text-h5 d-flex align-center">
          <v-icon color="error" class="mr-2">mdi-alert</v-icon>
          确认删除材料
        </v-card-title>
        <v-card-text>
          <v-alert type="warning" variant="tonal" class="mb-4">
            <strong>警告：</strong>此操作不可撤销！
          </v-alert>
          <div v-if="materialToDeleteFromList">
            <p>您确定要删除以下材料吗？</p>
            <v-card variant="outlined" class="mt-3">
              <v-card-text>
                <div class="d-flex align-center mb-2">
                  <v-icon color="success" class="mr-2">mdi-atom</v-icon>
                  <span class="font-weight-medium" v-html="formatFormula(materialToDeleteFromList.Formula)"></span>
                </div>
                <div class="text-caption text-grey">
                  ID: {{ materialToDeleteFromList.id }}
                </div>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            @click="showDeleteConfirmDialog = false"
            :disabled="submitting"
          >
            取消
          </v-btn>
          <v-btn
            color="error"
            @click="deleteMaterialFromList"
            :loading="submitting"
            :disabled="submitting"
          >
            确认删除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 成功提示 -->
    <UnifiedSnackbar
      v-model="showSnackbar"
      :message="snackbarText"
      :color="snackbarColor"
    />

    <!-- 自定义属性对话框 -->
    <v-dialog v-model="customPropertyDialog" max-width="600">
      <v-card>
        <v-card-title class="text-h5 d-flex align-center">
          <v-icon color="primary" class="mr-2">mdi-plus-circle-outline</v-icon>
          {{ editingCustomPropertyIndex >= 0 ? '编辑自定义属性' : '添加自定义属性' }}
        </v-card-title>
        <v-card-text>
          <v-form @submit.prevent="editingCustomPropertyIndex >= 0 ? updateCustomProperty() : addCustomProperty()">
            <v-text-field
              v-model="newCustomProperty.key"
              label="属性名称"
              variant="outlined"
              density="comfortable"
              :rules="[v => !!v || '属性名称不能为空']"
              placeholder="例如：Curie_Temperature"
              class="mb-4"
            ></v-text-field>

            <v-select
              v-model="newCustomProperty.type"
              label="数据类型"
              :items="customPropertyTypes"
              item-title="text"
              item-value="value"
              variant="outlined"
              density="comfortable"
              class="mb-4"
            ></v-select>

            <v-text-field
              v-if="newCustomProperty.type === 'string'"
              v-model="newCustomProperty.value"
              label="属性值"
              variant="outlined"
              density="comfortable"
              placeholder="输入文本值"
              class="mb-4"
            ></v-text-field>

            <v-text-field
              v-else-if="newCustomProperty.type === 'number'"
              v-model.number="newCustomProperty.value"
              label="属性值"
              type="number"
              variant="outlined"
              density="comfortable"
              placeholder="输入数字值"
              class="mb-4"
            ></v-text-field>

            <v-switch
              v-else-if="newCustomProperty.type === 'boolean'"
              v-model="newCustomProperty.value"
              label="属性值"
              color="primary"
              class="mb-4"
            ></v-switch>

            <v-text-field
              v-model="newCustomProperty.unit"
              label="单位（可选）"
              variant="outlined"
              density="comfortable"
              placeholder="例如：K, eV, g/cm³"
              class="mb-4"
            ></v-text-field>

            <v-textarea
              v-model="newCustomProperty.description"
              label="描述（可选）"
              variant="outlined"
              density="comfortable"
              rows="3"
              placeholder="对该属性的简要描述"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            @click="customPropertyDialog = false"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            @click="editingCustomPropertyIndex >= 0 ? updateCustomProperty() : addCustomProperty()"
          >
            {{ editingCustomPropertyIndex >= 0 ? '更新' : '添加' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios'
import JSZip from 'jszip'
import { handleError } from '@/utils/errorHandler'
import UnifiedSnackbar from '@/components/UnifiedSnackbar.vue'

// 将字段分组，便于管理和展示
const fieldGroups = [
  {
    title: '基本信息',
    icon: 'mdi-card-account-details-outline',
    fields: ['id', 'Formula', 'Structure', 'DOI']
  },
  {
    title: '晶体结构参数',
    icon: 'mdi-cube-outline',
    fields: ['a', 'b', 'c', 'a1', 'b1', 'c1', 'V_unit_cell']
  },
  {
    title: '对称性信息',
    icon: 'mdi-hexagon-multiple-outline',
    fields: ['Space_group_num', 'Space_group_sym', 'Point_group', 'Crystal_system', 'magnetic_lattice']
  },
  {
    title: '物理性质',
    icon: 'mdi-atom',
    fields: ['Density', 'Mole_Mass', 'Mole_Volume', 'GGA_band_gap', 'E_hull']
  },
  {
    title: '磁性参数',
    icon: 'mdi-magnet',
    fields: ['Mag_ion', 'Mag_ion_V', 'Mag_ion_m', 'Magnetic_moment', 'Effective_Spin']
  },
  {
    title: '热力学参数',
    icon: 'mdi-thermometer',
    fields: ['S_GS_Vol', 'S_GS_Mass', 'T_N', 'T_access', 'Nm_Rln_2J_1_', 'S_GS_mol', 'S_GS_mol2']
  },
  {
    title: '其他参数',
    icon: 'mdi-dots-horizontal',
    fields: ['Nearest_Mag_Ion_Distance_In_plane', 'Nearest_Mag_Ion_Distance_inter_plane']
  },
  {
    title: '自定义属性',
    icon: 'mdi-plus-circle-outline',
    fields: [], // 动态字段，不在这里定义
    isCustom: true // 标记为自定义属性组
  }
]

const getInitialFormData = () => ({
  id: '',
  Structure: '',
  Formula: '',
  a: '',
  b: '',
  c: '',
  a1: '',
  b1: '',
  c1: '',
  Space_group_num: '',
  Space_group_sym: '',
  Point_group: '',
  Crystal_system: '',
  magnetic_lattice: '',
  Density: '',
  V_unit_cell: '',
  Mole_Mass: '',
  Mole_Volume: '',
  GGA_band_gap: '',
  E_hull: '',
  Mag_ion: '',
  Mag_ion_V: '',
  Mag_ion_m: '',
  Magnetic_moment: '',
  Effective_Spin: '',
  S_GS_Vol: '',
  S_GS_Mass: '',
  T_N: '',
  T_access: '',
  Nm_Rln_2J_1_: '',
  S_GS_mol: '',
  S_GS_mol2: '',
  Nearest_Mag_Ion_Distance_In_plane: '',
  Nearest_Mag_Ion_Distance_inter_plane: '',
  DOI: '',
  // 自定义属性
  customProperties: []
})

// 字段标签映射，使显示更友好
const fieldLabels = {
  id: 'ID',
  Structure: '结构式',
  Formula: '化学式',
  a: '晶格常数 a (Å)',
  b: '晶格常数 b (Å)',
  c: '晶格常数 c (Å)',
  a1: '晶格角 α (°)',
  b1: '晶格角 β (°)',
  c1: '晶格角 γ (°)',
  Space_group_num: '空间群编号',
  Space_group_sym: '空间群符号',
  Point_group: '点群',
  Crystal_system: '晶系',
  magnetic_lattice: '磁晶格',
  Density: '密度 (g/cm³)',
  V_unit_cell: '单胞体积 (nm³)',
  Mole_Mass: '摩尔质量 (g/mol)',
  Mole_Volume: '摩尔体积 (cm³/mol)',
  GGA_band_gap: 'GGA带隙 (eV)',
  E_hull: 'E_hull (eV/atom)',
  Mag_ion: '磁离子',
  Mag_ion_V: '磁离子/体积 (nm⁻³)',
  Mag_ion_m: '磁离子/质量 (g⁻¹)',
  Magnetic_moment: '磁矩 (μB)',
  Effective_Spin: '有效自旋 (J)',
  S_GS_Vol: 'S_GS(Vol) (mJ/K⋅cm³)',
  S_GS_Mass: 'S_GS(Mass) (mJ/K⋅g)',
  T_N: '奈尔温度 (mK)',
  T_access: '可达温度 (mK)',
  Nm_Rln_2J_1_: 'Nm·Rln(2J+1)',
  S_GS_mol: 'S_GS(mol) (J/K⋅mol)',
  S_GS_mol2: 'S_GS(mol)2 (J/K⋅mol)',
  Nearest_Mag_Ion_Distance_In_plane: '面内最近磁离子距离',
  Nearest_Mag_Ion_Distance_inter_plane: '面间最近磁离子距离',
  DOI: 'DOI'
}

export default {
  name: 'Manage',

  components: {
    UnifiedSnackbar
  },

  data() {
    return {
      selectedOperation: '新增',
      operations: [
        { value: '新增', text: '新增数据', icon: 'mdi-database-plus', requiresUser: true },
        { value: '修改', text: '修改数据', icon: 'mdi-database-edit', requiresUser: true },
        { value: '删除', text: '删除数据', icon: 'mdi-database-remove', requiresUser: true },
        { value: '数据导出', text: '数据导出', icon: 'mdi-download', adminOnly: true },
        { value: '我的材料', text: '我的材料', icon: 'mdi-account-box', requiresUser: true }
      ],
      selectedMaterialId: '',
      formData: getInitialFormData(),
      materialToModify: null,
      materialToDelete: null,
      isUploading: false,
      uploadProgress: 0,
      processedFiles: 0,
      totalFiles: 0,
      currentProcessingFile: '',
      // 批量上传结果
      batchUploadResult: null,
      loading: false,
      submitting: false,
      isValid: false,
      error: '',
      showDeleteAllDialog: false,
      showSnackbar: false,
      snackbarText: '',
      snackbarColor: 'success',
      fieldGroups,
      fieldLabels,
      requiredFields: ['id', 'Formula', 'Structure'], // 必填字段
      currentUser: null,
      userRole: null,
      isAdmin: false,
      // 搜索我的材料相关
      myMaterialsSearch: '',
      myMaterialsResults: [],
      myMaterialsLoading: false,
      myMaterialsSearched: false,
      // CIF文件上传相关
      cifFile: null,
      cifFileRules: [
        value => !!value || 'CIF文件是必填项',
        value => !value || value.size < 10 * 1024 * 1024 || 'CIF文件大小不能超过10MB',
        value => !value || value.name.toLowerCase().endsWith('.cif') || '只支持.cif格式文件'
      ],
      // JSON文件解析状态
      jsonParsed: false,
      jsonFileName: '',
      // JSON示例展示控制
      showJsonExample: false,
      // ZIP结构展示控制
      showZipStructure: false,
      // 删除确认对话框
      showDeleteConfirmDialog: false,
      materialToDeleteFromList: null,
      // 自定义属性管理
      customPropertyDialog: false,
      newCustomProperty: {
        key: '',
        value: '',
        type: 'string',
        unit: '',
        description: ''
      },
      customPropertyTypes: [
        { value: 'string', text: '文本' },
        { value: 'number', text: '数字' },
        { value: 'boolean', text: '布尔值' }
      ],
      editingCustomPropertyIndex: -1,
      // 数据导出相关
      exportLoading: false,
      // JSON示例数据
      jsonExample: `{
  "id": "",
  "Structure": "",
  "Formula": "",
  "a": "",
  "b": "",
  "c": "",
  "a1": "",
  "b1": "",
  "c1": "",
  "Space_group_num": "",
  "Space_group_sym": "",
  "Point_group": "",
  "Crystal_system": "",
  "magnetic_lattice": "",
  "Density": "",
  "V_unit_cell": "",
  "Mole_Mass": "",
  "Mole_Volume": "",
  "GGA_band_gap": "",
  "E_hull": "",
  "Mag_ion": "",
  "Mag_ion_V": "",
  "Mag_ion_m": "",
  "Magnetic_moment": "",
  "Effective_Spin": "",
  "S_GS_Vol": "",
  "S_GS_Mass": "",
  "T_N": "",
  "T_access": "",
  "Nm_Rln_2J_1_": "",
  "S_GS_mol": "",
  "S_GS_mol2": "",
  "Nearest_Mag_Ion_Distance_In_plane": "",
  "Nearest_Mag_Ion_Distance_inter_plane": "",
  "DOI": ""
}`,
      // ZIP文件结构示例
      zipStructureExample: `materials.zip
├── material1/
│   ├── data.json          # 材料数据
│   └── Fe2O3.cif         # CIF文件
├── material2/
│   ├── data.json
│   └── NaCl.cif
├── material3/
│   ├── data.json
│   └── SiO2.cif
└── subfolder/            # 支持嵌套文件夹
    └── material4/
        ├── data.json
        └── CaCO3.cif`
    }
  },

  computed: {
    // 修复linter错误：将v-for和v-if分离
    formDataEntries() {
      return Object.entries(this.formData).filter(([key]) => key !== 'id');
    },

    // 检查用户是否可以访问某个操作
    canAccessOperation() {
      return (operation) => {
        // 管理员可以访问所有操作
        if (this.isAdmin) return true;

        // 如果操作需要管理员权限，非管理员不能访问
        if (operation.adminOnly) return false;

        // 如果操作需要用户权限（非游客），检查用户角色
        if (operation.requiresUser) {
          return this.userRole !== 'guest';
        }

        // 其他操作都可以访问
        return true;
      };
    },

    // 检查当前用户是否可以修改指定材料
    canModifyMaterial() {
      if (!this.materialToModify) return false;
      return this.isAdmin || this.materialToModify.createdBy === this.currentUser;
    },

    // 检查当前用户是否可以删除指定材料
    canDeleteMaterial() {
      if (!this.materialToDelete) return false;
      return this.isAdmin || this.materialToDelete.createdBy === this.currentUser;
    }
  },

  methods: {
    setOperation(operation) {
      this.selectedOperation = operation
      this.materialToModify = null
      this.materialToDelete = null
      this.selectedMaterialId = ''
      this.formData = getInitialFormData()
      // 重置我的材料状态
      this.myMaterialsSearch = ''
      this.myMaterialsResults = []
      this.myMaterialsSearched = false
      // 重置CIF文件和JSON解析状态
      this.cifFile = null
      this.jsonParsed = false
      this.jsonFileName = ''

      // 如果选择了"我的材料"，自动加载
      if (operation === '我的材料') {
        this.loadMyMaterials()
      }
    },

    getFieldType(key) {
      // 指定哪些字段是文本类型
      const textFields = ['id', 'Structure', 'Formula', 'Space_group_sym', 'Point_group', 
                          'Crystal_system', 'magnetic_lattice', 'Mag_ion', 'Nm_Rln_2J_1_', 
                          'Nearest_Mag_Ion_Distance_In_plane', 'Nearest_Mag_Ion_Distance_inter_plane', 'DOI'];
      
      return textFields.includes(key) ? 'text' : 'number';
    },

    // 获取字段标签
    getFieldLabel(key) {
      return this.fieldLabels[key] || key;
    },

    // 检查字段是否必填
    isRequired(key) {
      return this.requiredFields.includes(key);
    },

    // 将服务器数据转换为表单数据格式
    convertToFormData(serverData) {
      const formData = { ...serverData }
      const customProperties = []

      // 获取所有已知的固定字段
      const knownFields = new Set([
        'id', 'Structure', 'Formula', 'a', 'b', 'c', 'a1', 'b1', 'c1',
        'Space_group_num', 'Space_group_sym', 'Point_group', 'Crystal_system',
        'magnetic_lattice', 'Density', 'V_unit_cell', 'Mole_Mass', 'Mole_Volume',
        'GGA_band_gap', 'E_hull', 'Mag_ion', 'Mag_ion_V', 'Mag_ion_m',
        'Magnetic_moment', 'Effective_Spin', 'S_GS_Vol', 'S_GS_Mass', 'T_N',
        'T_access', 'Nm_Rln_2J_1_', 'S_GS_mol', 'S_GS_mol2',
        'Nearest_Mag_Ion_Distance_In_plane', 'Nearest_Mag_Ion_Distance_inter_plane',
        'DOI', 'createdBy', 'createdAt', 'lastModifiedBy', 'lastModifiedAt', 'Path',
        'customProperties'
      ])

      // 首先检查是否有正确格式的customProperties字段
      if (serverData.customProperties && typeof serverData.customProperties === 'object') {
        Object.keys(serverData.customProperties).forEach(key => {
          const prop = serverData.customProperties[key]
          if (prop && typeof prop === 'object' && Object.prototype.hasOwnProperty.call(prop, 'value')) {
            customProperties.push({
              id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
              key: key,
              value: prop.value,
              type: prop.type || 'string',
              unit: prop.unit || '',
              description: prop.description || ''
            })
          }
        })
      } else {
        // 处理旧的错误格式：查找分离的字段（如 Timi, Timi.value, Timi.type 等）
        const customPropGroups = {}

        Object.keys(serverData).forEach(key => {
          if (!knownFields.has(key)) {
            // 检查是否是分离格式的自定义属性
            if (key.includes('.')) {
              const [propName, propField] = key.split('.')
              if (!customPropGroups[propName]) {
                customPropGroups[propName] = {}
              }
              customPropGroups[propName][propField] = serverData[key]
              // 从表单数据中移除这个分离的字段
              delete formData[key]
            } else {
              // 检查是否是正确格式的嵌套对象
              if (typeof serverData[key] === 'object' && serverData[key] !== null) {
                const prop = serverData[key]
                if (Object.prototype.hasOwnProperty.call(prop, 'value') && Object.prototype.hasOwnProperty.call(prop, 'type')) {
                  customProperties.push({
                    id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
                    key: key,
                    value: prop.value,
                    type: prop.type || 'string',
                    unit: prop.unit || '',
                    description: prop.description || ''
                  })
                  // 从表单数据中移除这个自定义属性
                  delete formData[key]
                }
              } else {
                // 可能是分离格式的主字段，检查是否有对应的子字段
                const hasSubFields = Object.keys(serverData).some(k => k.startsWith(key + '.'))
                if (hasSubFields) {
                  if (!customPropGroups[key]) {
                    customPropGroups[key] = {}
                  }
                  customPropGroups[key].value = serverData[key]
                  // 从表单数据中移除这个字段
                  delete formData[key]
                }
              }
            }
          }
        })

        // 将分离格式的属性组合成完整的自定义属性
        Object.keys(customPropGroups).forEach(propName => {
          const propGroup = customPropGroups[propName]
          if (propGroup.value !== undefined) {
            customProperties.push({
              id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
              key: propName,
              value: propGroup.value,
              type: propGroup.type || 'string',
              unit: propGroup.unit || '',
              description: propGroup.description || ''
            })
          }
        })
      }

      // 添加自定义属性数组
      formData.customProperties = customProperties

      return formData
    },

    // 自定义属性管理方法
    openCustomPropertyDialog() {
      this.newCustomProperty = {
        key: '',
        value: '',
        type: 'string',
        unit: '',
        description: ''
      };
      this.customPropertyDialog = true;
    },

    addCustomProperty() {
      if (!this.newCustomProperty.key.trim()) {
        this.showErrorMessage('属性名称不能为空');
        return;
      }

      // 检查属性名是否已存在
      const exists = this.formData.customProperties.some(
        prop => prop.key === this.newCustomProperty.key.trim()
      );
      if (exists) {
        this.showErrorMessage('属性名称已存在');
        return;
      }

      // 添加新属性
      const newProperty = {
        id: Date.now().toString(), // 简单的ID生成
        key: this.newCustomProperty.key.trim(),
        value: this.newCustomProperty.value,
        type: this.newCustomProperty.type,
        unit: this.newCustomProperty.unit.trim(),
        description: this.newCustomProperty.description.trim()
      };

      this.formData.customProperties.push(newProperty);
      this.customPropertyDialog = false;
      this.showSuccessMessage(`成功添加自定义属性：${newProperty.key}`);
    },

    removeCustomProperty(index) {
      const property = this.formData.customProperties[index];
      this.formData.customProperties.splice(index, 1);
      this.showSuccessMessage(`已删除自定义属性：${property.key}`);
    },

    editCustomProperty(index) {
      const property = this.formData.customProperties[index];
      this.newCustomProperty = { ...property };
      this.customPropertyDialog = true;
      // 标记为编辑模式
      this.editingCustomPropertyIndex = index;
    },

    updateCustomProperty() {
      if (!this.newCustomProperty.key.trim()) {
        this.showErrorMessage('属性名称不能为空');
        return;
      }

      // 检查属性名是否与其他属性冲突（排除自己）
      const exists = this.formData.customProperties.some(
        (prop, index) => prop.key === this.newCustomProperty.key.trim() && index !== this.editingCustomPropertyIndex
      );
      if (exists) {
        this.showErrorMessage('属性名称已存在');
        return;
      }

      // 更新属性
      this.formData.customProperties[this.editingCustomPropertyIndex] = {
        ...this.newCustomProperty,
        key: this.newCustomProperty.key.trim(),
        unit: this.newCustomProperty.unit.trim(),
        description: this.newCustomProperty.description.trim()
      };

      this.customPropertyDialog = false;
      this.editingCustomPropertyIndex = -1;
      this.showSuccessMessage(`成功更新自定义属性：${this.newCustomProperty.key}`);
    },

    showSuccessMessage(text) {
      this.snackbarText = text
      this.snackbarColor = 'success'
      this.showSnackbar = true
    },

    showErrorMessage(text) {
      this.snackbarText = text
      this.snackbarColor = 'error'
      this.showSnackbar = true
    },

    // 获取当前用户信息
    async fetchCurrentUserInfo() {
      try {
        const response = await axios.get('/auth/user-info')
        this.currentUser = response.data.username
        this.userRole = response.data.role
        this.isAdmin = response.data.isAdmin
      } catch (error) {
        const errorInfo = handleError(error, { context: '获取用户信息' })
        console.error('获取用户信息失败:', errorInfo)
        // 如果获取用户信息失败，可能是未登录
        this.$router.push('/login')
      }
    },

    // 导出全部数据
    async exportAllData() {
      if (!this.isAdmin) {
        this.showErrorMessage('只有管理员可以导出数据');
        return;
      }

      this.exportLoading = true;
      try {
        // 调用后端导出API
        const response = await axios.get('/admin/export/all-materials', {
          responseType: 'blob', // 重要：指定响应类型为blob
          timeout: 300000 // 5分钟超时，因为导出可能需要较长时间
        });

        // 从响应头获取文件名
        const contentDisposition = response.headers['content-disposition'];
        let fileName = 'MCMD_Export.zip';
        if (contentDisposition) {
          const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (fileNameMatch && fileNameMatch[1]) {
            fileName = fileNameMatch[1].replace(/['"]/g, '');
          }
        }

        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/zip' });
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL对象
        window.URL.revokeObjectURL(downloadUrl);

        this.showSuccessMessage(`数据导出成功！文件已下载：${fileName}`);

      } catch (error) {
        console.error('导出数据失败:', error);

        let errorMessage = '导出失败，请稍后重试';
        if (error.response) {
          const status = error.response.status;
          if (status === 403) {
            errorMessage = '您没有导出数据的权限';
          } else if (status === 401) {
            errorMessage = '用户未认证，请重新登录';
          } else if (error.response.data && typeof error.response.data === 'string') {
            errorMessage = error.response.data;
          } else if (error.response.data && error.response.data.error) {
            errorMessage = error.response.data.error;
          }
        } else if (error.code === 'ECONNABORTED') {
          errorMessage = '导出超时，请稍后重试';
        }

        this.showErrorMessage(errorMessage);
      } finally {
        this.exportLoading = false;
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) {
        return 'N/A';
      }

      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return 'N/A';
        }

        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      } catch (e) {
        return 'N/A';
      }
    },

    async handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      try {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target.result)

            // 保存当前的CIF文件状态
            const currentCifFile = this.cifFile

            // 填充表单数据
            this.formData = { ...getInitialFormData(), ...data }

            // 恢复CIF文件状态（不重置已上传的CIF文件）
            this.cifFile = currentCifFile

            // 标记JSON已解析
            this.jsonParsed = true
            this.jsonFileName = file.name

            // 根据是否已有CIF文件显示不同的提示信息
            if (this.cifFile) {
              this.showSuccessMessage(`JSON文件 "${file.name}" 解析成功，CIF文件已保留，可以直接提交`)
            } else {
              this.showSuccessMessage(`JSON文件 "${file.name}" 解析成功，请上传CIF文件后提交`)
            }

            event.target.value = ''
          } catch (error) {
            this.error = '文件格式错误或数据无效'
            this.showErrorMessage('JSON文件解析失败：文件格式错误或数据无效')
          }
        }
        reader.readAsText(file)
      } catch (error) {
        this.error = '文件读取失败'
        this.showErrorMessage('JSON文件读取失败')
      }
    },

    async handleZipUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      // 重置状态
      this.isUploading = true
      this.uploadProgress = 0
      this.processedFiles = 0
      this.totalFiles = 0
      this.currentProcessingFile = ''
      this.batchUploadResult = null

      const results = {
        successes: [],
        failures: [],
        totalCount: 0,
        successCount: 0,
        failureCount: 0,
        hasErrors: false
      }

      try {
        const zip = new JSZip()
        const zipContent = await zip.loadAsync(file)

        // 查找所有材料文件夹（包含data.json的文件夹）
        const materialFolders = await this.findMaterialFolders(zipContent)
        this.totalFiles = materialFolders.length
        results.totalCount = materialFolders.length

        if (materialFolders.length === 0) {
          throw new Error('ZIP文件中未找到有效的材料数据。请确保每个材料文件夹包含data.json文件。')
        }

        // 处理每个材料文件夹
        for (const folder of materialFolders) {
          this.currentProcessingFile = folder.name
          this.processedFiles++
          this.uploadProgress = (this.processedFiles / this.totalFiles) * 100

          try {
            const result = await this.processMaterialFolder(zipContent, folder)
            results.successes.push(result)
            results.successCount++
          } catch (error) {
            console.error(`Error processing ${folder.name}:`, error)
            results.failures.push({
              fileName: folder.name,
              filePath: folder.path,
              errorType: this.getErrorType(error),
              errorMessage: error.message,
              suggestion: this.getErrorSuggestion(error)
            })
            results.failureCount++
            results.hasErrors = true
          }
        }

        // 设置结果
        this.batchUploadResult = results

        // 显示总体结果消息
        if (results.successCount > 0 && results.failureCount === 0) {
          this.showSuccessMessage(`批量上传完成！成功上传 ${results.successCount} 个材料`)
        } else if (results.successCount > 0 && results.failureCount > 0) {
          this.showErrorMessage(`批量上传完成！成功 ${results.successCount} 个，失败 ${results.failureCount} 个`)
        } else {
          this.showErrorMessage(`批量上传失败！所有 ${results.failureCount} 个材料都上传失败`)
        }

      } catch (error) {
        console.error('Error processing ZIP file:', error)
        this.showErrorMessage(`ZIP文件处理失败：${error.message}`)
      } finally {
        this.isUploading = false
        this.currentProcessingFile = ''
        event.target.value = ''
      }
    },

    // 查找ZIP文件中的材料文件夹
    async findMaterialFolders(zipContent) {
      const folders = []
      const fileNames = Object.keys(zipContent.files)

      // 查找包含data.json的文件夹
      const dataJsonFiles = fileNames.filter(name =>
        name.endsWith('data.json') && !zipContent.files[name].dir
      )

      for (const dataJsonPath of dataJsonFiles) {
        const folderPath = dataJsonPath.substring(0, dataJsonPath.lastIndexOf('/'))
        const folderName = folderPath.split('/').pop() || 'root'

        // 查找同一文件夹中的CIF文件
        const cifFiles = fileNames.filter(name =>
          name.startsWith(folderPath + '/') &&
          name.endsWith('.cif') &&
          !zipContent.files[name].dir
        )

        folders.push({
          name: folderName,
          path: folderPath,
          dataJsonPath: dataJsonPath,
          cifFiles: cifFiles
        })
      }

      return folders
    },

    // 处理单个材料文件夹
    async processMaterialFolder(zipContent, folder) {
      // 读取data.json
      const jsonContent = await zipContent.files[folder.dataJsonPath].async('text')
      const materialData = JSON.parse(jsonContent)

      // 验证必填字段
      if (!materialData.Formula) {
        throw new Error('data.json中缺少必填字段：Formula')
      }

      if (!materialData.id) {
        throw new Error('data.json中缺少必填字段：id')
      }

      // 查找匹配的CIF文件
      let cifFile = null
      const expectedCifName = `${materialData.Formula}.cif`

      // 首先查找与Formula匹配的CIF文件
      const matchingCif = folder.cifFiles.find(cifPath =>
        cifPath.endsWith(expectedCifName)
      )

      if (matchingCif) {
        const cifContent = await zipContent.files[matchingCif].async('blob')
        cifFile = new File([cifContent], expectedCifName, { type: 'chemical/x-cif' })
      } else if (folder.cifFiles.length > 0) {
        // 如果没有匹配的，使用第一个CIF文件
        const cifPath = folder.cifFiles[0]
        const cifContent = await zipContent.files[cifPath].async('blob')
        const cifFileName = cifPath.split('/').pop()
        cifFile = new File([cifContent], cifFileName, { type: 'chemical/x-cif' })
      } else {
        throw new Error('文件夹中未找到CIF文件')
      }

      // 调用现有的提交方法
      const formData = new FormData()
      formData.append('data', JSON.stringify(materialData))
      formData.append('cifFile', cifFile)

      const response = await axios.post('/materials/add-with-cif', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return {
        id: materialData.id,
        formula: materialData.Formula,
        response: response.data
      }
    },

    // 获取错误类型
    getErrorType(error) {
      if (error.message.includes('ID重复') || error.message.includes('already exists')) {
        return 'ID重复'
      } else if (error.message.includes('Formula')) {
        return '字段缺失'
      } else if (error.message.includes('CIF')) {
        return 'CIF文件问题'
      } else if (error.message.includes('JSON')) {
        return 'JSON格式错误'
      } else {
        return '未知错误'
      }
    },

    // 获取错误建议
    getErrorSuggestion(error) {
      if (error.message.includes('ID重复')) {
        return '请修改材料ID，确保唯一性'
      } else if (error.message.includes('Formula')) {
        return '请在data.json中添加Formula字段'
      } else if (error.message.includes('CIF')) {
        return '请确保文件夹中包含有效的CIF文件'
      } else if (error.message.includes('JSON')) {
        return '请检查JSON文件格式是否正确'
      } else {
        return '请检查文件结构和数据格式'
      }
    },

    async fetchMaterialById(materialId, operation) {
      if (!materialId) {
        this.showErrorMessage('请输入材料ID')
        return
      }

      this.loading = true
      try {
        const response = await axios.get(`/materials/MCMD-${materialId}`)
        if (!response.data) {
          this.showErrorMessage(`ID为 MCMD-${materialId} 的材料不存在`)
          return
        }
        
        if (operation === 'modify') {
          this.materialToModify = response.data



          // 处理自定义属性：将扁平结构转换为自定义属性数组
          const processedData = this.convertToFormData(response.data)

          this.formData = { ...getInitialFormData(), ...processedData }
        } else if (operation === 'delete') {
          this.materialToDelete = response.data
        }
      } catch (error) {
        // 根据HTTP状态码显示不同的错误消息
        let errorMessage = `查询ID为 MCMD-${materialId} 的材料失败`

        if (error.response) {
          const status = error.response.status
          const serverMessage = error.response.data?.message || error.response.data

          if (status === 404) {
            errorMessage = `材料 MCMD-${materialId} 不存在`
          } else if (serverMessage) {
            errorMessage = typeof serverMessage === 'string' ? serverMessage : `查询ID为 MCMD-${materialId} 的材料失败`
          }
        }

        this.showErrorMessage(errorMessage)
      } finally {
        this.loading = false
      }
    },

    async submitData(event) {
      const submitData = event instanceof SubmitEvent ? this.formData : event

      if (!submitData.id) {
        this.error = '请输入ID'
        return
      }

      // 检查CIF文件是否已上传（必填项）
      if (!this.cifFile) {
        this.error = 'CIF文件是必填项，请上传CIF文件'
        this.showErrorMessage('CIF文件是必填项，请上传CIF文件')
        return
      }

      // 检查Formula字段（CIF文件上传需要此字段）
      if (!submitData.Formula) {
        this.error = 'Formula字段是必填项'
        this.showErrorMessage('Formula字段是必填项，CIF文件上传需要此字段')
        return
      }

      const materialId = submitData.id.startsWith('MCMD-') ? submitData.id : `MCMD-${submitData.id}`

      this.submitting = true
      try {
        // 使用带CIF文件上传的API（现在CIF文件是必填的）
        const formData = new FormData()

        // 处理自定义属性：将其转换为扁平结构
        const processedData = { ...submitData, id: materialId }
        if (submitData.customProperties && submitData.customProperties.length > 0) {
          // 创建自定义属性对象
          const customPropsObject = {}
          submitData.customProperties.forEach(prop => {
            customPropsObject[prop.key] = {
              value: prop.value,
              type: prop.type,
              unit: prop.unit,
              description: prop.description
            }
          })
          // 将自定义属性作为一个整体添加到数据中
          processedData.customProperties = customPropsObject
        } else {
          // 如果没有自定义属性，确保字段存在但为空对象
          processedData.customProperties = {}
        }



        formData.append('data', JSON.stringify(processedData))
        formData.append('cifFile', this.cifFile)

        await axios.post('/materials/add-with-cif', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (event instanceof SubmitEvent) {
          this.formData = getInitialFormData()
          this.cifFile = null
          this.jsonParsed = false
          this.jsonFileName = ''
          this.showSuccessMessage('成功添加 1 条数据')
        }
        this.error = ''
      } catch (error) {
        const errorInfo = handleError(error, { context: '添加材料' })
        this.error = errorInfo.message
        if (event instanceof SubmitEvent) {
          this.showErrorMessage(this.error)
        }
        throw error
      } finally {
        this.submitting = false
      }
    },

    async updateData() {
      if (!this.selectedMaterialId) return

      this.submitting = true
      try {
        // 处理自定义属性：将其转换为扁平结构
        const processedData = { ...this.formData }
        if (this.formData.customProperties && this.formData.customProperties.length > 0) {
          // 创建自定义属性对象
          const customPropsObject = {}
          this.formData.customProperties.forEach(prop => {
            customPropsObject[prop.key] = {
              value: prop.value,
              type: prop.type,
              unit: prop.unit,
              description: prop.description
            }
          })
          // 将自定义属性作为一个整体添加到数据中
          processedData.customProperties = customPropsObject
        } else {
          // 如果没有自定义属性，确保字段存在但为空对象
          processedData.customProperties = {}
        }

        // 如果有CIF文件，使用带文件上传的API
        if (this.cifFile) {
          const formData = new FormData()
          formData.append('data', JSON.stringify(processedData))
          formData.append('cifFile', this.cifFile)

          await axios.post(`/materials/MCMD-${this.selectedMaterialId}/update-with-cif`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
        } else {
          // 没有CIF文件，使用原有API
          await axios.put(`/update_material/MCMD-${this.selectedMaterialId}`, processedData)
        }

        this.formData = getInitialFormData()
        this.cifFile = null
        this.selectedMaterialId = ''
        this.materialToModify = null
        this.showSuccessMessage('修改成功')
      } catch (error) {
        const errorInfo = handleError(error, { context: '修改材料' })
        this.error = errorInfo.message
        this.showErrorMessage(this.error)
      } finally {
        this.submitting = false
      }
    },

    async deleteData() {
      if (!this.selectedMaterialId) return

      this.submitting = true
      try {
        await axios.delete(`/delete_material/MCMD-${this.selectedMaterialId}`)
        this.selectedMaterialId = ''
        this.materialToDelete = null
        this.showSuccessMessage('成功删除 1 条数据')
      } catch (error) {
        this.error = error.response?.data?.message || '删除失败，请稍后重试'
        this.showErrorMessage(this.error)
      } finally {
        this.submitting = false
      }
    },

    confirmDeleteAll() {
      // 检查管理员权限
      if (!this.isAdmin) {
        this.showErrorMessage('只有管理员可以执行删除全部数据操作')
        return
      }
      this.showDeleteAllDialog = true
    },

    async deleteAllMaterials() {
      // 双重检查管理员权限
      if (!this.isAdmin) {
        this.showErrorMessage('只有管理员可以执行删除全部数据操作')
        this.showDeleteAllDialog = false
        return
      }

      this.submitting = true
      try {
        const response = await axios.delete('/delete_all_materials')
        const deletedCount = response.data?.deleted_count || 0
        const cifDeletedCount = response.data?.cif_deleted_count || 0
        const cifFailedCount = response.data?.cif_failed_count || 0

        this.showDeleteAllDialog = false

        let message = `成功删除 ${deletedCount} 条数据`
        if (cifDeletedCount > 0 || cifFailedCount > 0) {
          message += `，CIF文件：${cifDeletedCount} 个删除成功`
          if (cifFailedCount > 0) {
            message += `，${cifFailedCount} 个删除失败`
          }
        }

        this.showSuccessMessage(message)
      } catch (error) {
        this.error = error.response?.data?.message || '删除失败，请稍后重试'
        this.showErrorMessage(this.error)
      } finally {
        this.submitting = false
      }
    },

    cancelDeletion() {
      this.materialToDelete = null
      this.selectedMaterialId = ''
    },

    // 复制JSON示例
    async copyJsonExample() {
      try {
        await navigator.clipboard.writeText(this.jsonExample)
        this.showSuccessMessage('JSON示例已复制到剪贴板')
      } catch (error) {
        this.showErrorMessage('复制失败，请手动复制')
      }
    },

    // 加载我的材料
    async loadMyMaterials() {
      try {
        this.myMaterialsLoading = true;
        this.myMaterialsSearched = true;

        // 调用后端API，不传任何搜索参数，获取全部我的材料
        const response = await axios.get('/materials/my-materials');

        this.myMaterialsResults = response.data.materials || [];

      } catch (error) {
        this.showErrorMessage('加载我的材料失败，请稍后重试');
        this.myMaterialsResults = [];
      } finally {
        this.myMaterialsLoading = false;
      }
    },

    // 导航到材料详情页
    navigateToMaterial(materialId) {
      this.$router.push(`/material/${materialId}`);
    },

    // 格式化化学式
    formatFormula(formula) {
      if (!formula) return 'N/A';
      return formula.replace(/(\d+)/g, '<sub>$1</sub>');
    },

    // 编辑材料 - 切换到修改页面并填充数据
    async editMaterial(material) {
      // 切换到修改操作
      this.selectedOperation = '修改';

      // 设置材料ID
      this.selectedMaterialId = material.id.replace('MCMD-', '');

      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });

      try {
        // 从服务器获取完整的材料信息（包括贡献者信息）
        await this.fetchMaterialById(this.selectedMaterialId, 'modify');
        this.showSuccessMessage(`已加载材料 ${material.id} 的完整数据，可以开始修改`);
      } catch (error) {
        // 如果获取失败，使用列表中的数据作为备用
        this.materialToModify = material;
        const processedData = this.convertToFormData(material)
        this.formData = { ...getInitialFormData(), ...processedData };
        this.showSuccessMessage(`已加载材料 ${material.id} 的基本数据，可以开始修改`);
      }
    },

    // 确认删除材料
    confirmDeleteMaterial(material) {
      this.materialToDeleteFromList = material;
      this.showDeleteConfirmDialog = true;
    },

    // 从列表中删除材料
    async deleteMaterialFromList() {
      if (!this.materialToDeleteFromList) return;

      this.submitting = true;
      try {
        await axios.delete(`/delete_material/${this.materialToDeleteFromList.id}`);

        const deletedMaterialId = this.materialToDeleteFromList.id;

        // 从列表中移除已删除的材料
        this.myMaterialsResults = this.myMaterialsResults.filter(
          material => material.id !== this.materialToDeleteFromList.id
        );

        this.showDeleteConfirmDialog = false;
        this.materialToDeleteFromList = null;
        this.showSuccessMessage(`成功删除材料 ${deletedMaterialId}`);
      } catch (error) {
        const errorMessage = error.response?.data?.message || '删除失败，请稍后重试';
        this.showErrorMessage(errorMessage);
      } finally {
        this.submitting = false;
      }
    }
  },

  async mounted() {
    await this.fetchCurrentUserInfo()
  },

  beforeUnmount() {
    // 清理定时器，防止内存泄漏
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer);
      this.uploadTimer = null;
    }

    // 清理其他可能的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }

    // 清理任何未完成的异步操作
    this.submitting = false;
    this.isUploading = false;
  }
}
</script>

<style scoped>
.manage-page {
  background-color: #F5F5F5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.operation-btn {
  min-width: 120px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.gap-4 {
  gap: 16px;
}

.material-data {
  background: #f8f9fa;
  font-family: 'Fira Code', 'Roboto Mono', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

.material-data pre {
  margin: 0;
}

/* 禁用所有浮动效果，但排除材料卡片 */
.v-card:not(.material-card) {
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  box-shadow: none !important;
  transition: none !important;
}

.v-card:not(.material-card):hover {
  box-shadow: none !important;
  transform: none !important;
}

/* 禁用所有按钮的浮动效果，但排除材料卡片内的按钮 */
.v-btn:not(.material-card .v-btn) {
  box-shadow: none !important;
  transition: none !important;
}

.v-btn:not(.material-card .v-btn):hover {
  box-shadow: none !important;
  transform: none !important;
}

/* 禁用所有输入框的浮动效果 */
.v-text-field,
.v-select,
.v-file-input,
.v-textarea {
  box-shadow: none !important;
  transition: none !important;
}

.v-text-field:hover,
.v-select:hover,
.v-file-input:hover,
.v-textarea:hover {
  box-shadow: none !important;
  transform: none !important;
}

/* 禁用所有芯片的浮动效果，但排除材料卡片内的芯片 */
.v-chip:not(.material-card .v-chip) {
  box-shadow: none !important;
  transition: none !important;
}

.v-chip:not(.material-card .v-chip):hover {
  box-shadow: none !important;
  transform: none !important;
}

/* 字段分组卡片样式 */
.field-group-card {
  border: 1px solid rgba(25, 118, 210, 0.15) !important;
}

.group-title {
  background-color: rgba(25, 118, 210, 0.05);
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

/* 背景渐变 */
.bg-gradient {
  background: linear-gradient(135deg, #1976d2, #42a5f5) !important;
}

.white--text {
  color: white !important;
}

/* 字体样式 */
.v-card-title,
.text-h5,
.text-h6,
.v-btn,
.v-text-field,
.v-select {
  font-family: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
}

.v-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.text-h5 {
  font-size: 1.5rem !important;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.text-h6 {
  font-weight: 600;
}

.v-btn {
  font-weight: 600;
}

.v-text-field,
.v-select {
  font-weight: 500;
}



/* 我的材料搜索相关样式 */
.material-card {
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease !important;
}

.material-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(76, 175, 80, 0.3) !important;
}

.material-card .v-card-title {
  color: #2e7d32;
}

/* JSON示例样式 */

.json-example-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  overflow: hidden;
}

.json-example-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #1565c0;
  background-color: #fafafa;
  padding: 16px;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.cursor-pointer {
  cursor: pointer;
}

.rotate-180 {
  transform: rotate(180deg);
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* ZIP结构样式 */

.zip-structure-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff8e1;
  overflow: hidden;
}

.zip-structure-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #e65100;
  background-color: #fff8e1;
  padding: 16px;
  margin: 0;
  max-height: 250px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>