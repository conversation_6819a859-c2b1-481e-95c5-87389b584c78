import { createRouter, createWebHistory } from 'vue-router'
import { auth } from '@/utils/auth'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('../views/ForgotPassword.vue')
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('../views/ResetPassword.vue')
  },
  {
    path: '/change-password',
    name: 'ChangePassword',
    component: () => import('../views/ChangePassword.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/verify-email',
    name: 'EmailVerification',
    component: () => import('../views/EmailVerification.vue')
  },
  {
    path: '/registration-success',
    name: 'RegistrationSuccess',
    component: () => import('../views/RegistrationSuccess.vue')
  },
  {
    path: '/manage',
    name: 'Manage',
    component: () => import('../views/Manage.vue'),
    meta: { requiresAuth: true, requiresNonGuest: true }
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('../views/UserManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/material/:id',
    name: 'MaterialDetail',
    component: () => import('../views/MaterialDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/cookie-debug',
    name: 'CookieDebug',
    component: () => import('../views/CookieDebug.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果用户使用浏览器的前进/后退按钮，则恢复到之前的位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则，滚动到页面顶部
    return { top: 0 }
  }
})

// 导航守卫
// 验证重定向URL是否安全（防止开放重定向攻击）
function isValidRedirectUrl(url) {
  if (!url || typeof url !== 'string') return false

  // 只允许相对路径和当前域名的路径
  if (url.startsWith('/')) {
    // 防止双斜杠绕过（//evil.com）
    return !url.startsWith('//')
  }

  // 不允许绝对URL
  return false
}

router.beforeEach((to, from, next) => {
  const isLoggedIn = auth.isLoggedIn()

  // 如果用户已登录且试图访问登录、注册、忘记密码或重置密码页面
  if ((to.path === '/login' || to.path === '/register' || to.path === '/forgot-password' || to.path === '/reset-password') && isLoggedIn) {
    // 验证重定向地址的安全性
    const redirect = to.query.redirect || '/'
    const safeRedirect = isValidRedirectUrl(redirect) ? redirect : '/'
    next(safeRedirect)
    return
  }
  
  // 如果路由需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 检查是否需要管理员权限
      if (to.matched.some(record => record.meta.requiresAdmin)) {
        if (!auth.isAdmin()) {
          // 非管理员用户访问管理员页面，重定向到首页
          next('/')
          return
        }
      }

      // 检查是否需要非游客权限
      if (to.matched.some(record => record.meta.requiresNonGuest)) {
        if (auth.isGuest()) {
          // 游客用户访问需要权限的页面，重定向到首页
          next('/')
          return
        }
      }

      next()
    }
  } else {
    next()
  }
})

export default router
