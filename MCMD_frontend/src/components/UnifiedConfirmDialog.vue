<template>
  <v-dialog v-model="show" max-width="500" persistent>
    <v-card>
      <v-card-title class="text-h5 pa-6 d-flex align-center">
        <v-icon :color="iconColor" class="mr-2" size="28">{{ icon }}</v-icon>
        {{ title }}
        <v-spacer></v-spacer>
        <v-chip 
          v-if="showAdminChip"
          color="orange" 
          variant="outlined" 
          size="small" 
          prepend-icon="mdi-shield-account"
        >
          管理员操作
        </v-chip>
      </v-card-title>
      
      <v-card-text class="pa-6">
        <!-- 警告提示 -->
        <v-alert
          :type="alertType"
          variant="tonal"
          border="start"
          :icon="alertIcon"
          class="mb-4"
        >
          <strong>{{ alertTitle }}</strong><br>
          {{ alertMessage }}
        </v-alert>
        
        <!-- 详细内容 -->
        <div v-if="detailContent">
          <p>{{ message }}</p>
          <v-card variant="outlined" class="mt-3">
            <v-card-text>
              <slot name="detail-content">
                {{ detailContent }}
              </slot>
            </v-card-text>
          </v-card>
        </div>
        
        <!-- 简单消息 -->
        <p v-else>{{ message }}</p>
      </v-card-text>
      
      <v-card-actions class="pa-6">
        <v-spacer></v-spacer>
        <v-btn
          variant="outlined"
          @click="cancel"
          :disabled="loading"
        >
          取消
        </v-btn>
        <v-btn
          :color="confirmButtonColor"
          @click="confirm"
          :loading="loading"
          :disabled="loading"
        >
          {{ confirmText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'UnifiedConfirmDialog',
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '确认操作'
    },
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'warning', // 'warning', 'error', 'info'
      validator: value => ['warning', 'error', 'info'].includes(value)
    },
    confirmText: {
      type: String,
      default: '确认'
    },
    loading: {
      type: Boolean,
      default: false
    },
    showAdminChip: {
      type: Boolean,
      default: false
    },
    detailContent: {
      type: String,
      default: null
    },
    customAlertMessage: {
      type: String,
      default: null
    }
  },
  
  computed: {
    show: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    
    icon() {
      switch (this.type) {
        case 'error':
          return 'mdi-alert-circle'
        case 'warning':
          return 'mdi-alert'
        case 'info':
          return 'mdi-information'
        default:
          return 'mdi-alert'
      }
    },
    
    iconColor() {
      switch (this.type) {
        case 'error':
          return 'error'
        case 'warning':
          return 'warning'
        case 'info':
          return 'info'
        default:
          return 'warning'
      }
    },
    
    alertType() {
      return this.type === 'warning' ? 'warning' : this.type
    },
    
    alertIcon() {
      return this.icon
    },
    
    alertTitle() {
      switch (this.type) {
        case 'error':
          return '危险操作警告'
        case 'warning':
          return '警告'
        case 'info':
          return '提示'
        default:
          return '警告'
      }
    },
    
    alertMessage() {
      // 如果有自定义消息，使用自定义消息
      if (this.customAlertMessage) {
        return this.customAlertMessage
      }

      // 默认消息
      switch (this.type) {
        case 'error':
          return '此操作具有风险且不可撤销，请确认是否继续？'
        case 'warning':
          return '此操作不可撤销，请确认是否继续？'
        case 'info':
          return '请确认是否执行此操作？'
        default:
          return '此操作不可撤销，请确认是否继续？'
      }
    },
    
    confirmButtonColor() {
      switch (this.type) {
        case 'error':
          return 'error'
        case 'warning':
          return 'warning'
        case 'info':
          return 'primary'
        default:
          return 'warning'
      }
    }
  },
  
  methods: {
    confirm() {
      this.$emit('confirm')
    },
    
    cancel() {
      this.show = false
      this.$emit('cancel')
    }
  }
}
</script>
