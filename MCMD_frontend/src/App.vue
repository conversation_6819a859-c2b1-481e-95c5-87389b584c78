<template>
  <v-app>
    <v-app-bar color="primary" dark class="pr-2">
      <v-app-bar-title class="d-flex align-center pa-0">
        <router-link to="/" class="text-white text-decoration-none d-flex align-center">
          <div>
            <v-img
              src="/MCMD_newLogo.png"
              alt="Logo"
              width="32"
              height="32"
              class="logo-image"
            />
          </div>
          <!-- 响应式标题显示 -->
          <div class="ml-2">
            <!-- 大屏幕显示完整标题 -->
            <span class="d-none d-lg-inline">Magnetocaloric Materials Database (MCMD)</span>
            <!-- 中等屏幕显示简化标题 -->
            <span class="d-none d-md-inline d-lg-none">Materials Database (MCMD)</span>
            <!-- 小屏幕只显示MCMD -->
            <span class="d-inline d-md-none">MCMD</span>
          </div>
        </router-link>
      </v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- 响应式导航按钮 -->
      <v-btn to="/" text class="text-uppercase d-none d-sm-flex ml-2">
        Home
      </v-btn>
      <v-btn to="/" icon class="d-flex d-sm-none ml-2">
        <v-icon>mdi-home</v-icon>
      </v-btn>

      <template v-if="!isLoggedIn">
        <v-btn to="/about" text class="text-uppercase d-none d-md-flex ml-2">
          About
        </v-btn>
        <v-btn to="/about" icon class="d-flex d-md-none ml-2">
          <v-icon>mdi-information</v-icon>
        </v-btn>

        <v-btn to="/login" text class="text-uppercase d-none d-sm-flex ml-2">
          Login
        </v-btn>
        <v-btn to="/login" icon class="d-flex d-sm-none ml-2">
          <v-icon>mdi-login</v-icon>
        </v-btn>
      </template>

      <template v-else>
        <!-- About 按钮 -->
        <v-btn to="/about" text class="text-uppercase d-none d-md-flex ml-2">
          About
        </v-btn>
        <v-btn to="/about" icon class="d-flex d-md-none ml-2">
          <v-icon>mdi-information</v-icon>
        </v-btn>

        <!-- AI Chat 按钮 -->
        <v-btn
          href="http://*************:8080/ui/chat/eafd0d897d37dbcc"
          target="_blank"
          icon
          class="ml-2"
        >
          <v-icon>mdi-chat-processing</v-icon>
          <v-tooltip
            activator="parent"
            location="bottom"
          >
            AI Chat
          </v-tooltip>
        </v-btn>

        <!-- 用户菜单 -->
        <v-menu offset-y>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              icon
              class="user-menu-btn"
            >
              <v-avatar size="36" color="white">
                <span class="text-primary font-weight-bold">
                  {{ getUserInitial() }}
                </span>
              </v-avatar>
            </v-btn>
          </template>

          <v-list class="user-menu-list">
            <!-- 用户信息显示 -->
            <v-list-item class="user-info-item">
              <template v-slot:prepend>
                <v-avatar size="40" color="primary">
                  <span class="text-white font-weight-bold">
                    {{ getUserInitial() }}
                  </span>
                </v-avatar>
              </template>
              <v-list-item-title class="font-weight-bold">
                {{ username }}
              </v-list-item-title>
              <v-list-item-subtitle>
                <v-chip
                  :color="getRoleColor()"
                  size="x-small"
                  variant="flat"
                  class="text-white"
                >
                  {{ getRoleText() }}
                </v-chip>
              </v-list-item-subtitle>
            </v-list-item>

            <v-divider></v-divider>

            <!-- 数据管理 - 游客不可见 -->
            <v-list-item
              v-if="!isGuest"
              to="/manage"
              prepend-icon="mdi-database-cog"
              title="Data Management"
              subtitle="Manage material database"
            ></v-list-item>

            <!-- 用户管理 - 仅管理员可见 -->
            <v-list-item
              v-if="isAdmin"
              to="/user-management"
              prepend-icon="mdi-account-group"
              title="User Management"
              subtitle="Manage system users"
            ></v-list-item>

            <v-divider></v-divider>

            <!-- 修改密码 -->
            <v-list-item
              to="/change-password"
              prepend-icon="mdi-lock-reset"
              title="修改密码"
              subtitle="Change your password"
            ></v-list-item>

            <v-divider></v-divider>

            <!-- 登出 -->
            <v-list-item
              @click="logout"
              prepend-icon="mdi-logout"
              title="Sign Out"
              subtitle="Logout from system"
              class="logout-item"
            ></v-list-item>
          </v-list>
        </v-menu>
      </template>
    </v-app-bar>

    <v-main>
      <router-view v-slot="{ Component, route }">
        <transition
          :name="getTransitionName(route)"
          mode="out-in"
          appear
        >
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </v-main>
  </v-app>
</template>

<script>
import { auth } from '@/utils/auth'
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'App',
  setup() {
    const router = useRouter()
    const username = ref(sessionStorage.getItem('username'))
    const isLoggedIn = ref(auth.isLoggedIn())
    const isAdmin = ref(auth.isAdmin())
    const isGuest = ref(auth.isGuest())



    const logout = async () => {
      try {
        await auth.logout()
        username.value = null
        isLoggedIn.value = false
        isAdmin.value = false
        isGuest.value = true
        router.push('/')
      } catch (error) {
        console.error('Logout failed:', error)
      }
    }

    // 监听登录状态变化
    const checkLoginStatus = () => {
      username.value = sessionStorage.getItem('username')
      isLoggedIn.value = auth.isLoggedIn()
      isAdmin.value = auth.isAdmin()
      isGuest.value = auth.isGuest()
    }

    // 定义事件处理函数，确保清理时使用相同的引用
    const handleLoginSuccess = () => {
      checkLoginStatus()
      if (isLoggedIn.value) {
        loadAIChatScript()
      }
    }

    // 处理全局登出事件
    const handleAuthLogout = () => {
      username.value = null
      isLoggedIn.value = false
      isAdmin.value = false
      isGuest.value = true
      // 清理AI聊天脚本
      const existingScript = document.querySelector('script[data-ai-chat-script]')
      if (existingScript) {
        existingScript.remove()
      }
    }

    onMounted(() => {
      checkLoginStatus()
      window.addEventListener('storage', checkLoginStatus)
      window.addEventListener('login-success', handleLoginSuccess)
      window.addEventListener('auth-logout', handleAuthLogout)

      // 监听登录状态变化，只在登录后加载AI Chat脚本
      watch(isLoggedIn, (newValue) => {
        if (newValue) {
          // 用户已登录，加载AI Chat脚本
          loadAIChatScript()
        }
      }, { immediate: true })
    })

    onUnmounted(() => {
      window.removeEventListener('storage', checkLoginStatus)
      window.removeEventListener('login-success', handleLoginSuccess)
      window.removeEventListener('auth-logout', handleAuthLogout)
    })
    
    // 加载AI Chat脚本的函数
    const loadAIChatScript = () => {
      // 检查脚本是否已加载
      if (document.querySelector('script[data-ai-chat-script]')) {
        return
      }
      
      // 动态添加外部脚本
      const script = document.createElement('script')
      script.async = true
      script.defer = true
      script.src = 'http://*************:8080/api/application/embed?protocol=http&host=*************:8080&token=eafd0d897d37dbcc'
      script.setAttribute('data-ai-chat-script', 'true')
      document.body.appendChild(script)
    }

    // 获取路由过渡动画名称
    const getTransitionName = (route) => {
      // 为登录和注册页面添加特殊的过渡效果
      if (route.path === '/login' || route.path === '/register') {
        return 'auth-slide'
      }
      // 其他页面使用默认的淡入淡出效果
      return 'fade'
    }

    // 获取用户名首字母
    const getUserInitial = () => {
      if (!username.value) return 'U'
      return username.value.charAt(0).toUpperCase()
    }

    const getRoleColor = () => {
      const role = auth.getUserRole()
      switch (role) {
        case 'admin':
          return 'red-darken-1'      // 红色 - 管理员
        case 'user':
          return 'green-darken-1'    // 绿色 - 普通用户
        case 'guest':
          return 'orange-darken-1'   // 橙色 - 游客
        default:
          return 'grey-darken-1'     // 灰色 - 默认
      }
    }

    const getRoleText = () => {
      const role = auth.getUserRole()
      switch (role) {
        case 'admin':
          return 'Administrator'
        case 'user':
          return 'User'
        case 'guest':
          return 'Guest'
        default:
          return 'Unknown'
      }
    }

    return {
      username,
      isLoggedIn,
      isAdmin,
      isGuest,
      logout,
      loadAIChatScript,
      getTransitionName,
      getUserInitial,
      getRoleColor,
      getRoleText
    }
  }

  // 注意：不需要Options API的mounted钩子，因为Composition API的onMounted已经处理了所有逻辑
}
</script>

<style>
.v-application {
  font-family: 'Roboto', sans-serif;
  background-color: #F5F5F5 !important;
}

.main-container {
  height: 100%;
}

.position-relative {
  position: relative;
  height: 100%;
}

.main-content {
  transition: all 0.3s ease-in-out;
  width: 100%;
  margin-left: 0;
}

.content-shifted {
  width: 66.667%;
  margin-left: 33.333%;
}

.v-main {
  background-color: #F5F5F5 !important;
  padding-top: 64px !important;
}

.main-container {
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}



/* 聊天窗口样式 */
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chat-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.chat-window {
  position: relative;
  width: 90%;
  max-width: 800px;
  height: 80vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1001;
}

.logo-image {
  background-color: white;
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.v-application {
  background-color: #f5f5f5;
}

.app-bar {
  border-bottom: 1px solid #e0e0e0;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 右上角按钮样式 */
.v-app-bar .v-btn {
  font-size: 15px;
  font-weight: 500;
  text-transform: uppercase !important;
}

/* 响应式按钮样式 */
@media (max-width: 600px) {
  .v-app-bar .v-btn {
    min-width: 40px !important;
    padding: 0 8px !important;
  }

  .v-app-bar .v-btn.v-btn--icon {
    width: 40px !important;
    height: 40px !important;
  }
}

/* 确保所有带有text-uppercase类的元素都保持大写 */
.text-uppercase {
  text-transform: uppercase !important;
}

/* 响应式标题样式 */
.v-app-bar-title {
  flex: 1 1 auto;
  min-width: 0; /* 允许flex项目缩小 */
}

.v-app-bar-title .text-white {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 确保标题文本在不同屏幕尺寸下的字体大小 */
@media (max-width: 960px) {
  .v-app-bar-title {
    font-size: 1.1rem !important;
  }
}

@media (max-width: 600px) {
  .v-app-bar-title {
    font-size: 1rem !important;
  }

  /* 在小屏幕上减少logo和文字的间距 */
  .v-app-bar-title .ml-2 {
    margin-left: 8px !important;
  }
}

/* 路由过渡动画 */
/* 认证页面过渡效果 - 与卡片动画一致 */
.auth-slide-enter-active,
.auth-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.auth-slide-enter-from {
  opacity: 0;
  transform: translateY(8px);
}

.auth-slide-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

/* 默认淡入淡出效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* AI Chat 方形按钮样式 */
.ai-chat-btn {
  height: 36px !important;
  width: 36px !important;
  border-radius: 4px !important;
  min-width: 36px !important;
}

/* 用户菜单样式 */
.user-menu-btn {
  margin-left: 8px;
}

.user-menu-btn .v-avatar {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.user-menu-btn:hover .v-avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.user-menu-list {
  min-width: 280px;
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.user-info-item {
  margin: 8px;
  padding: 12px 16px !important;
  pointer-events: none; /* 禁用悬停效果 */
  background: transparent !important;
}

.user-info-item .v-list-item-title {
  font-size: 1.1rem;
  color: #1976d2;
}

.user-info-item .v-list-item-subtitle {
  margin-top: 4px;
}

.v-list-item {
  border-radius: 6px !important;
  margin: 4px 8px;
  transition: all 0.2s ease-in-out;
}

.v-list-item:hover:not(.user-info-item) {
  background-color: rgba(25, 118, 210, 0.08) !important;
  border-radius: 6px !important;
}

.v-list-item:active:not(.user-info-item),
.v-list-item:focus:not(.user-info-item),
.v-list-item.v-list-item--active:not(.user-info-item) {
  background-color: rgba(25, 118, 210, 0.12) !important;
  border-radius: 6px !important;
}

.v-list-item:active:not(.user-info-item)::before,
.v-list-item:focus:not(.user-info-item)::before,
.v-list-item.v-list-item--active:not(.user-info-item)::before {
  border-radius: 6px !important;
}

.logout-item:hover {
  background-color: rgba(244, 67, 54, 0.08) !important;
}

.logout-item:hover .v-list-item__prepend .v-icon {
  color: #f44336 !important;
}
</style>
