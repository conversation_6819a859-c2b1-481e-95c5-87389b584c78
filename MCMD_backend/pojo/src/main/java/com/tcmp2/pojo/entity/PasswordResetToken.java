package com.tcmp2.pojo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 密码重置令牌实体类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 用于密码重置功能的令牌存储
 */
@Data
@Document(collection = "password_reset_tokens")
public class PasswordResetToken {

    @Id
    @Field("_id")
    private String mongoId;

    /** 用户ID */
    @Field("userId")
    private String userId;

    /** 用户名 */
    @Field("username")
    private String username;

    /** 重置令牌 */
    @Field("token")
    private String token;

    /** 过期时间 */
    @Field("expiryDate")
    private Date expiryDate;

    /** 创建时间 */
    @Field("createdAt")
    private Date createdAt;

    /** 是否已使用 */
    @Field("used")
    private Boolean used;
}
