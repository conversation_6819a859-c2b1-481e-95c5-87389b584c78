package com.tcmp2.pojo.enums;

/**
 * 用户角色枚举
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 10:05
 * @description : 定义系统中的用户角色
 */
public enum UserRole {

    /** 游客用户 - 只能查询数据库和查看材料详情 */
    GUEST("guest", "游客用户"),

    /** 普通用户 - 可以管理自己的数据 */
    USER("user", "普通用户"),

    /** 管理员 - 可以管理所有数据和用户权限 */
    ADMIN("admin", "管理员");
    
    private final String code;
    private final String description;
    
    UserRole(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取角色
     */
    public static UserRole fromCode(String code) {
        for (UserRole role : values()) {
            if (role.code.equals(code)) {
                return role;
            }
        }
        return GUEST; // 默认返回游客用户
    }

    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * 检查是否为游客
     */
    public boolean isGuest() {
        return this == GUEST;
    }

    /**
     * 检查是否为普通用户
     */
    public boolean isUser() {
        return this == USER;
    }

    /**
     * 检查是否可以修改数据（普通用户或管理员）
     */
    public boolean canModifyData() {
        return this == USER || this == ADMIN;
    }

    /**
     * 检查是否可以管理用户（仅管理员）
     */
    public boolean canManageUsers() {
        return this == ADMIN;
    }

    /**
     * 获取权限级别（数字越大权限越高）
     */
    public int getLevel() {
        switch (this) {
            case GUEST: return 1;
            case USER: return 2;
            case ADMIN: return 3;
            default: return 0;
        }
    }
}
