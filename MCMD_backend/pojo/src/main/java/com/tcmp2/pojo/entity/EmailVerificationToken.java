package com.tcmp2.pojo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 邮箱验证令牌实体类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 用于邮箱验证的令牌信息
 */
@Data
@Document(collection = "email_verification_tokens")
public class EmailVerificationToken {

    @Id
    @Field("_id")
    private String mongoId;

    /** 用户ID */
    @Field("userId")
    private String userId;

    /** 用户名 */
    @Field("username")
    private String username;

    /** 邮箱地址 */
    @Field("email")
    private String email;

    /** 验证令牌 */
    @Field("token")
    private String token;

    /** 过期时间 */
    @Field("expiryDate")
    private Date expiryDate;

    /** 创建时间 */
    @Field("createdAt")
    private Date createdAt;

    /** 是否已使用 */
    @Field("used")
    private boolean used;

    /** 验证时间 */
    @Field("verifiedAt")
    private Date verifiedAt;
}
