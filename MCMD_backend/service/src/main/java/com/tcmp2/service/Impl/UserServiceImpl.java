package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.EmailVerificationService;
import com.tcmp2.service.UserService;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 用户管理服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 10:00
 * @description : 用户注册、管理相关服务实现
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private static final String USERS_COLLECTION = "users";
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Resource
    private AuthorizationService authorizationService;

    @Resource
    private EmailVerificationService emailVerificationService;
    
    private final Argon2 argon2 = Argon2Factory.create(Argon2Factory.Argon2Types.ARGON2id);
    
    // 邮箱格式验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    @Override
    public Map<String, Object> register(String username, String password, String email, String realName, String organization) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                response.put("error", "Username cannot be empty");
                return response;
            }

            if (password == null || password.length() < 6) {
                response.put("error", "Password must be at least 6 characters long");
                return response;
            }

            if (email == null || !EMAIL_PATTERN.matcher(email).matches()) {
                response.put("error", "Invalid email format");
                return response;
            }

            // 检查用户名是否已存在
            if (isUsernameExists(username.trim())) {
                response.put("error", "Username already exists");
                return response;
            }

            // 检查邮箱是否已存在
            if (isEmailExists(email.trim())) {
                response.put("error", "Email already registered");
                return response;
            }
            
            // 创建新用户
            User newUser = new User();
            newUser.setUsername(username.trim());
            newUser.setPassword(argon2.hash(2, 65536, 1, password.toCharArray()));
            newUser.setEmail(email.trim());
            newUser.setRealName(realName != null ? realName.trim() : "");
            newUser.setOrganization(organization != null ? organization.trim() : "");
            newUser.setRole(UserRole.GUEST.getCode()); // 新用户默认为游客角色
            newUser.setStatus("pending"); // 待邮箱验证状态
            newUser.setCreatedAt(new Date());

            // 保存用户
            mongoTemplate.save(newUser, USERS_COLLECTION);

            // 发送邮箱验证邮件
            Map<String, Object> emailResult = emailVerificationService.sendVerificationEmail(
                newUser.getMongoId(), username.trim(), email.trim());

            if (!(Boolean) emailResult.get("success")) {
                // 如果邮件发送失败，删除刚创建的用户
                mongoTemplate.remove(new Query(Criteria.where("_id").is(newUser.getMongoId())), USERS_COLLECTION);
                response.put("error", "邮件发送失败，请稍后重试");
                return response;
            }

            logger.info("新用户注册成功，等待邮箱验证: {}, 角色: {}", username, UserRole.GUEST.getCode());
            response.put("message", "Registration successful. Please check your email to verify your account.");
            response.put("username", username);
            response.put("email", email.trim());
            response.put("needVerification", true);
            
        } catch (Exception e) {
            logger.error("用户注册失败: {}", e.getMessage(), e);
            response.put("error", "注册失败，请稍后重试");
        }
        
        return response;
    }
    
    @Override
    public List<User> getAllUsers(String currentUser) {
        // 检查权限：只有管理员可以查看所有用户
        if (!authorizationService.isAdmin(currentUser)) {
            throw new SecurityException("Insufficient permissions, only administrators can view user list");
        }
        
        try {
            Query query = new Query();
            // 不返回密码字段
            query.fields().exclude("password");
            return mongoTemplate.find(query, User.class, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("获取用户列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户列表失败");
        }
    }
    
    @Override
    public Map<String, String> updateUserRole(String currentUser, String targetUsername, UserRole newRole) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // 检查权限：只有管理员可以修改用户角色
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "Insufficient permissions, only administrators can modify user roles");
                return response;
            }

            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (targetUser == null) {
                response.put("error", "Target user not found");
                return response;
            }

            // 防止管理员修改自己的角色（避免系统无管理员）
            if (currentUser.equals(targetUsername) && newRole != UserRole.ADMIN) {
                response.put("error", "Cannot downgrade your own administrator privileges");
                return response;
            }
            
            // 更新用户角色
            Update update = new Update()
                .set("role", newRole.getCode())
                .set("lastModifiedAt", new Date());
            
            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);
            
            logger.info("管理员 {} 将用户 {} 的角色更新为 {}", currentUser, targetUsername, newRole.getCode());
            response.put("message", "用户角色更新成功");
            response.put("username", targetUsername);
            response.put("newRole", newRole.getCode());
            
        } catch (Exception e) {
            logger.error("更新用户角色失败: {}", e.getMessage(), e);
            response.put("error", "更新用户角色失败");
        }
        
        return response;
    }
    
    @Override
    public Map<String, String> updateUserStatus(String currentUser, String targetUsername, String status) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // 检查权限：只有管理员可以修改用户状态
            if (!authorizationService.isAdmin(currentUser)) {
                response.put("error", "Insufficient permissions, only administrators can modify user status");
                return response;
            }

            // 验证状态值 - 只支持 active 和 banned
            if (!status.equals("active") && !status.equals("banned")) {
                response.put("error", "Invalid status value, only 'active' or 'banned' are supported");
                return response;
            }

            // 检查目标用户是否存在
            Query query = new Query(Criteria.where("username").is(targetUsername));
            User targetUser = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

            if (targetUser == null) {
                response.put("error", "Target user not found");
                return response;
            }

            // 防止管理员禁用自己
            if (currentUser.equals(targetUsername) && status.equals("banned")) {
                response.put("error", "Cannot disable your own account");
                return response;
            }
            
            // 更新用户状态
            Update update = new Update()
                .set("status", status)
                .set("lastModifiedAt", new Date());
            
            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);
            
            logger.info("管理员 {} 将用户 {} 的状态更新为 {}", currentUser, targetUsername, status);
            response.put("message", "用户状态更新成功");
            response.put("username", targetUsername);
            response.put("newStatus", status);
            
        } catch (Exception e) {
            logger.error("更新用户状态失败: {}", e.getMessage(), e);
            response.put("error", "更新用户状态失败");
        }
        
        return response;
    }
    
    @Override
    public User getUserInfo(String username) {
        try {
            Query query = new Query(Criteria.where("username").is(username));
            // 不返回密码字段
            query.fields().exclude("password");
            return mongoTemplate.findOne(query, User.class, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean isUsernameExists(String username) {
        try {
            Query query = new Query(Criteria.where("username").is(username));
            return mongoTemplate.exists(query, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("检查用户名是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean isEmailExists(String email) {
        try {
            Query query = new Query(Criteria.where("email").is(email));
            return mongoTemplate.exists(query, USERS_COLLECTION);
        } catch (Exception e) {
            logger.error("检查邮箱是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
