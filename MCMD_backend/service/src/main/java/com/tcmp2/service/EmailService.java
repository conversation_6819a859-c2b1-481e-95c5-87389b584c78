package com.tcmp2.service;

/**
 * 邮件服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 邮件发送相关服务
 */
public interface EmailService {
    
    /**
     * 发送密码重置邮件
     *
     * @param toEmail 收件人邮箱
     * @param username 用户名
     * @param resetLink 重置链接
     * @return 是否发送成功
     */
    boolean sendPasswordResetEmail(String toEmail, String username, String resetLink);
    
    /**
     * 发送密码修改成功通知邮件
     *
     * @param toEmail 收件人邮箱
     * @param username 用户名
     * @return 是否发送成功
     */
    boolean sendPasswordChangeNotification(String toEmail, String username);
}
