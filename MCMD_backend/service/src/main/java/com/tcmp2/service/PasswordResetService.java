package com.tcmp2.service;

import java.util.Map;

/**
 * 密码重置服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 密码重置相关服务
 */
public interface PasswordResetService {
    
    /**
     * 发送密码重置邮件
     *
     * @param usernameOrEmail 用户名或邮箱
     * @return 操作结果
     */
    Map<String, Object> sendPasswordResetEmail(String usernameOrEmail);
    
    /**
     * 验证重置令牌
     *
     * @param token 重置令牌
     * @return 验证结果
     */
    Map<String, Object> validateResetToken(String token);
    
    /**
     * 重置密码
     *
     * @param token 重置令牌
     * @param newPassword 新密码
     * @return 操作结果
     */
    Map<String, Object> resetPassword(String token, String newPassword);
    
    /**
     * 修改密码（已登录用户）
     *
     * @param username 用户名
     * @param currentPassword 当前密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    Map<String, Object> changePassword(String username, String currentPassword, String newPassword);
}
