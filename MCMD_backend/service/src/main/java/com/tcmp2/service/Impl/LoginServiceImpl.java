package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.LoginService;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 23:37
 * @description : 登录服务实现类
 */
@Service
public class LoginServiceImpl implements LoginService {

    private static final Logger logger = LoggerFactory.getLogger(LoginServiceImpl.class);
    private static final String USERS_COLLECTION = "users";

    // JWT配置 - 从配置文件读取
    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private long tokenValidity;

    @Value("${jwt.refresh-window}")
    private long refreshWindow;
    
    @Resource
    private MongoTemplate mongoTemplate;

    private final Argon2 argon2 = Argon2Factory.create(Argon2Factory.Argon2Types.ARGON2id);

    /**
     * 获取JWT密钥
     */
    private Key getJwtKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    @Override
    public Map<String, String> login(String usernameOrEmail, String password) {
        Map<String, String> response = new HashMap<>();

        logger.debug("Login attempt for: {}", usernameOrEmail);

        // 查询用户 - 支持用户名或邮箱登录
        Query query = new Query(new Criteria().orOperator(
            Criteria.where("username").is(usernameOrEmail),
            Criteria.where("email").is(usernameOrEmail)
        ));
        User user = mongoTemplate.findOne(query, User.class, USERS_COLLECTION);

        logger.debug("User found in database: {}", (user != null));

        if (user != null && user.getPassword() != null) {
            String storedHash = user.getPassword();
//            System.out.println("Stored password hash: " + storedHash);

            try {
                // 验证密码 - 支持多种哈希方式
                boolean verified = verifyPassword(password, storedHash);

                if (verified) {
                    // 检查用户状态
                    String userStatus = user.getStatus();
                    if (userStatus == null || !"active".equals(userStatus)) {
                        String statusMessage = getStatusMessage(userStatus);
                        response.put("message", statusMessage);
                        logger.warn("用户 {} 尝试登录但状态为: {}", user.getUsername(), userStatus);
                        return response;
                    }

                    // 更新最后登录时间
                    updateLastLoginTime(user.getUsername());

                    // 生成JWT令牌，包含用户角色信息
                    String userRole = user.getRole() != null ? user.getRole() : UserRole.USER.getCode();
                    String token = generateToken(user.getUsername(), userRole);

                    response.put("message", "Login successful");
                    response.put("username", user.getUsername());
                    response.put("role", userRole);
                    response.put("token", token);
                    return response;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 用户名或密码错误
        response.put("message", "Invalid username or password");
        return response;
    }

    @Override
    public Map<String, String> logout() {
        return Map.of("message", "Logout successful");
    }
    
    /**
     * 生成JWT令牌
     *
     * @param username 用户名
     * @param role 用户角色
     * @return JWT令牌
     */
    private String generateToken(String username, String role) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + tokenValidity);

        return Jwts.builder()
                .setSubject(username)
                .claim("role", role)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getJwtKey())
                .compact();
    }

    /**
     * 验证密码 - 支持Argon2和BCrypt
     *
     * @param plainPassword 明文密码
     * @param hashedPassword 哈希密码
     * @return 验证结果
     */
    private boolean verifyPassword(String plainPassword, String hashedPassword) {
        try {
            // 检查是否为BCrypt哈希 (以$2a$, $2b$, $2y$开头)
            if (hashedPassword.startsWith("$2a$") || hashedPassword.startsWith("$2b$") || hashedPassword.startsWith("$2y$")) {
                // 简单的BCrypt验证 - 这里我们需要一个简单的实现
                // 由于没有BCrypt库，我们暂时返回false，建议使用Argon2
                logger.warn("检测到BCrypt哈希，但当前不支持BCrypt验证。请使用Argon2版本的用户管理脚本。");
                return false;
            } else {
                // 使用Argon2验证
                return argon2.verify(hashedPassword, plainPassword.toCharArray());
            }
        } catch (Exception e) {
            logger.error("密码验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新用户最后登录时间
     *
     * @param username 用户名
     */
    private void updateLastLoginTime(String username) {
        try {
            Query query = new Query(Criteria.where("username").is(username));
            Update update = new Update().set("lastLoginAt", new Date());
            mongoTemplate.updateFirst(query, update, USERS_COLLECTION);
        } catch (Exception e) {
            // 记录错误但不影响登录流程
            logger.warn("更新用户最后登录时间失败: {}", e.getMessage());
        }
    }
    
    /**
     * 验证JWT令牌
     *
     * @param token JWT令牌
     * @return 如果有效，返回用户名；否则返回null
     */
    @Override
    public String validateToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getJwtKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            return claims.getSubject();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取令牌的过期时间
     *
     * @param token JWT令牌
     * @return 过期时间，如果令牌无效则返回null
     */
    private Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getJwtKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            return claims.getExpiration();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 检查令牌是否可以刷新（在刷新窗口期内）
     *
     * @param token JWT令牌
     * @return 是否可以刷新
     */
    private boolean canTokenBeRefreshed(String token) {
        Date expiration = getExpirationDateFromToken(token);
        if (expiration == null) {
            return false;
        }
        
        Date now = new Date();
        // 检查令牌是否已过期
        if (expiration.before(now)) {
            return false;
        }
        
        // 检查是否在刷新窗口期内
        long timeToExpiry = expiration.getTime() - now.getTime();
        return timeToExpiry <= refreshWindow;
    }
    
    @Override
    public Map<String, String> refreshToken(String token) {
        Map<String, String> response = new HashMap<>();
        
        // 验证令牌
        String username = validateToken(token);
        if (username == null) {
            response.put("message", "Invalid token");
            return response;
        }
        
        // 检查令牌是否可以刷新
        if (!canTokenBeRefreshed(token)) {
            // 如果令牌仍然有效但不在刷新窗口期内，返回原令牌
            Date expiration = getExpirationDateFromToken(token);
            if (expiration != null && expiration.after(new Date())) {
                response.put("message", "Token still valid");
                response.put("token", token);
                response.put("username", username);
                return response;
            }
            
            response.put("message", "Token cannot be refreshed");
            return response;
        }
        
        // 获取用户角色并生成新令牌
        Query userQuery = new Query(Criteria.where("username").is(username));
        User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);
        String userRole = (user != null && user.getRole() != null) ? user.getRole() : UserRole.USER.getCode();
        String newToken = generateToken(username, userRole);
        
        response.put("message", "Token refreshed successfully");
        response.put("token", newToken);
        response.put("username", username);
        
        return response;
    }

    /**
     * 根据用户状态获取相应的错误消息
     *
     * @param status 用户状态
     * @return 错误消息
     */
    private String getStatusMessage(String status) {
        if (status == null) {
            return "Account status is not set, please contact administrator";
        }

        switch (status) {
            case "pending":
                return "Account is pending email verification. Please check your email and verify your account.";
            case "banned":
                return "Account has been disabled, please contact administrator";
            default:
                return "Account status is invalid, please contact administrator";
        }
    }
}
