package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.EmailVerificationToken;
import com.tcmp2.pojo.entity.User;
import com.tcmp2.service.EmailService;
import com.tcmp2.service.EmailVerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 邮箱验证服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 处理邮箱验证相关业务逻辑
 */
@Service
public class EmailVerificationServiceImpl implements EmailVerificationService {

    private static final Logger logger = LoggerFactory.getLogger(EmailVerificationServiceImpl.class);
    private static final String TOKENS_COLLECTION = "email_verification_tokens";
    private static final String USERS_COLLECTION = "users";

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private EmailService emailService;

    @Override
    public Map<String, Object> sendVerificationEmail(String userId, String username, String email) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 生成验证令牌
            String token = UUID.randomUUID().toString();
            Date expiryDate = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000); // 24小时后过期

            // 删除该用户之前的未使用令牌
            Query deleteQuery = new Query(Criteria.where("userId").is(userId).and("used").is(false));
            mongoTemplate.remove(deleteQuery, TOKENS_COLLECTION);

            // 创建新的验证令牌
            EmailVerificationToken verificationToken = new EmailVerificationToken();
            verificationToken.setUserId(userId);
            verificationToken.setUsername(username);
            verificationToken.setEmail(email);
            verificationToken.setToken(token);
            verificationToken.setExpiryDate(expiryDate);
            verificationToken.setCreatedAt(new Date());
            verificationToken.setUsed(false);

            mongoTemplate.save(verificationToken, TOKENS_COLLECTION);

            // 发送验证邮件
            boolean emailSent = emailService.sendEmailVerification(email, username, token);

            if (emailSent) {
                response.put("success", true);
                response.put("message", "Verification email sent");
                logger.info("邮箱验证邮件已发送给用户: {}", username);
            } else {
                response.put("success", false);
                response.put("message", "Unable to send email. Please try again");
                logger.error("发送邮箱验证邮件失败: {}", email);
            }

        } catch (Exception e) {
            logger.error("发送邮箱验证邮件时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Something went wrong. Please try again");
        }

        return response;
    }

    @Override
    public Map<String, Object> verifyEmail(String token) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 查找验证令牌
            Query tokenQuery = new Query(Criteria.where("token").is(token));
            EmailVerificationToken verificationToken = mongoTemplate.findOne(tokenQuery, EmailVerificationToken.class, TOKENS_COLLECTION);

            if (verificationToken == null) {
                response.put("success", false);
                response.put("message", "无效的验证链接");
                return response;
            }

            // 检查令牌是否已使用
            if (verificationToken.isUsed()) {
                response.put("success", false);
                response.put("message", "验证链接已使用");
                return response;
            }

            // 检查令牌是否过期
            if (verificationToken.getExpiryDate().before(new Date())) {
                response.put("success", false);
                response.put("message", "验证链接已过期，请重新申请");
                return response;
            }

            // 激活用户账户
            Query userQuery = new Query(Criteria.where("_id").is(verificationToken.getUserId()));
            Update userUpdate = new Update()
                    .set("status", "active")
                    .set("emailVerified", true)
                    .set("emailVerifiedAt", new Date());
            mongoTemplate.updateFirst(userQuery, userUpdate, USERS_COLLECTION);

            // 标记令牌为已使用
            Update tokenUpdate = new Update()
                    .set("used", true)
                    .set("verifiedAt", new Date());
            mongoTemplate.updateFirst(tokenQuery, tokenUpdate, TOKENS_COLLECTION);

            response.put("success", true);
            response.put("message", "Email verified successfully! Your account is now active");
            response.put("username", verificationToken.getUsername());
            logger.info("用户 {} 邮箱验证成功", verificationToken.getUsername());

        } catch (Exception e) {
            logger.error("邮箱验证时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Something went wrong. Please try again");
        }

        return response;
    }

    @Override
    public Map<String, Object> resendVerificationEmail(String email) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 查找用户
            Query userQuery = new Query(Criteria.where("email").is(email));
            User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);

            if (user == null) {
                response.put("success", false);
                response.put("message", "No account found with this email address");
                return response;
            }

            // 检查用户是否已激活
            if ("active".equals(user.getStatus())) {
                response.put("success", false);
                response.put("message", "This email is already verified");
                return response;
            }

            // 重新发送验证邮件
            return sendVerificationEmail(user.getMongoId(), user.getUsername(), user.getEmail());

        } catch (Exception e) {
            logger.error("重新发送验证邮件时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Something went wrong. Please try again");
        }

        return response;
    }

    @Override
    public boolean isEmailVerified(String email) {
        try {
            Query userQuery = new Query(Criteria.where("email").is(email).and("status").is("active"));
            User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);
            return user != null;
        } catch (Exception e) {
            logger.error("检查邮箱验证状态时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }
}
