package com.tcmp2.service;

import java.util.Map;

/**
 * 邮箱验证服务接口
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 处理邮箱验证相关业务逻辑
 */
public interface EmailVerificationService {

    /**
     * 发送邮箱验证邮件
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param email 邮箱地址
     * @return 操作结果
     */
    Map<String, Object> sendVerificationEmail(String userId, String username, String email);

    /**
     * 验证邮箱令牌
     *
     * @param token 验证令牌
     * @return 验证结果
     */
    Map<String, Object> verifyEmail(String token);

    /**
     * 重新发送验证邮件
     *
     * @param email 邮箱地址
     * @return 操作结果
     */
    Map<String, Object> resendVerificationEmail(String email);

    /**
     * 检查邮箱是否已验证
     *
     * @param email 邮箱地址
     * @return 是否已验证
     */
    boolean isEmailVerified(String email);
}
