package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.PasswordResetToken;
import com.tcmp2.pojo.entity.User;
import com.tcmp2.service.EmailService;
import com.tcmp2.service.PasswordResetService;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 密码重置服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 密码重置服务实现
 */
@Service
public class PasswordResetServiceImpl implements PasswordResetService {
    
    private static final Logger logger = LoggerFactory.getLogger(PasswordResetServiceImpl.class);
    private static final String USERS_COLLECTION = "users";
    private static final String RESET_TOKENS_COLLECTION = "password_reset_tokens";
    
    @Resource
    private MongoTemplate mongoTemplate;
    
    @Resource
    private EmailService emailService;
    
    @Value("${app.frontend-url}")
    private String frontendUrl;
    
    @Value("${app.password-reset.token-expiry-minutes}")
    private int tokenExpiryMinutes;
    
    private final Argon2 argon2 = Argon2Factory.create(Argon2Factory.Argon2Types.ARGON2id);
    
    @Override
    public Map<String, Object> sendPasswordResetEmail(String usernameOrEmail) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 查找用户
            Query userQuery = new Query(new Criteria().orOperator(
                Criteria.where("username").is(usernameOrEmail),
                Criteria.where("email").is(usernameOrEmail)
            ));
            User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);
            
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return response;
            }
            
            // 检查用户状态
            if (!"active".equals(user.getStatus())) {
                response.put("success", false);
                response.put("message", "账户已被禁用，无法重置密码");
                return response;
            }
            
            // 删除该用户之前的重置令牌
            Query deleteQuery = new Query(Criteria.where("userId").is(user.getMongoId()));
            mongoTemplate.remove(deleteQuery, RESET_TOKENS_COLLECTION);
            
            // 生成新的重置令牌
            String resetToken = UUID.randomUUID().toString();
            Date expiryDate = new Date(System.currentTimeMillis() + tokenExpiryMinutes * 60 * 1000L);
            
            // 保存重置令牌
            PasswordResetToken tokenEntity = new PasswordResetToken();
            tokenEntity.setUserId(user.getMongoId());
            tokenEntity.setUsername(user.getUsername());
            tokenEntity.setToken(resetToken);
            tokenEntity.setExpiryDate(expiryDate);
            tokenEntity.setCreatedAt(new Date());
            tokenEntity.setUsed(false);
            
            mongoTemplate.save(tokenEntity, RESET_TOKENS_COLLECTION);
            
            // 构建重置链接
            String resetLink = frontendUrl + "/reset-password?token=" + resetToken;
            
            // 发送邮件
            boolean emailSent = emailService.sendPasswordResetEmail(user.getEmail(), user.getUsername(), resetLink);
            
            if (emailSent) {
                response.put("success", true);
                response.put("message", "重置邮件已发送到您的邮箱");
                logger.info("密码重置邮件发送成功: {}", user.getUsername());
            } else {
                response.put("success", false);
                response.put("message", "邮件发送失败，请稍后重试");
                logger.error("密码重置邮件发送失败: {}", user.getUsername());
            }
            
        } catch (Exception e) {
            logger.error("发送密码重置邮件时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "系统错误，请稍后重试");
        }
        
        return response;
    }
    
    @Override
    public Map<String, Object> validateResetToken(String token) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Query query = new Query(Criteria.where("token").is(token));
            PasswordResetToken resetToken = mongoTemplate.findOne(query, PasswordResetToken.class, RESET_TOKENS_COLLECTION);
            
            if (resetToken == null) {
                response.put("valid", false);
                response.put("message", "无效的重置链接");
                return response;
            }
            
            if (resetToken.getUsed() != null && resetToken.getUsed()) {
                response.put("valid", false);
                response.put("message", "重置链接已被使用");
                return response;
            }
            
            if (resetToken.getExpiryDate().before(new Date())) {
                response.put("valid", false);
                response.put("message", "重置链接已过期，请重新申请");
                return response;
            }
            
            response.put("valid", true);
            response.put("username", resetToken.getUsername());
            
        } catch (Exception e) {
            logger.error("验证重置令牌时发生错误: {}", e.getMessage(), e);
            response.put("valid", false);
            response.put("message", "系统错误");
        }
        
        return response;
    }
    
    @Override
    public Map<String, Object> resetPassword(String token, String newPassword) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证令牌
            Map<String, Object> tokenValidation = validateResetToken(token);
            if (!(Boolean) tokenValidation.get("valid")) {
                return tokenValidation;
            }
            
            // 验证新密码
            if (newPassword == null || newPassword.length() < 6) {
                response.put("success", false);
                response.put("message", "密码长度不能少于6位");
                return response;
            }
            
            // 获取重置令牌信息
            Query tokenQuery = new Query(Criteria.where("token").is(token));
            PasswordResetToken resetToken = mongoTemplate.findOne(tokenQuery, PasswordResetToken.class, RESET_TOKENS_COLLECTION);

            if (resetToken == null) {
                response.put("success", false);
                response.put("message", "无效的重置令牌");
                return response;
            }

            // 更新用户密码
            String hashedPassword = argon2.hash(2, 65536, 1, newPassword.toCharArray());
            Query userQuery = new Query(Criteria.where("_id").is(resetToken.getUserId()));
            Update userUpdate = new Update().set("password", hashedPassword);
            mongoTemplate.updateFirst(userQuery, userUpdate, USERS_COLLECTION);
            
            // 标记令牌为已使用
            Update tokenUpdate = new Update().set("used", true);
            mongoTemplate.updateFirst(tokenQuery, tokenUpdate, RESET_TOKENS_COLLECTION);
            
            // 获取用户信息发送通知邮件
            User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);
            if (user != null && user.getEmail() != null) {
                emailService.sendPasswordChangeNotification(user.getEmail(), user.getUsername());
            }
            
            response.put("success", true);
            response.put("message", "密码重置成功");
            logger.info("用户 {} 密码重置成功", resetToken.getUsername());
            
        } catch (Exception e) {
            logger.error("重置密码时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "系统错误，请稍后重试");
        }
        
        return response;
    }
    
    @Override
    public Map<String, Object> changePassword(String username, String currentPassword, String newPassword) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证新密码
            if (newPassword == null || newPassword.length() < 6) {
                response.put("success", false);
                response.put("message", "密码长度不能少于6位");
                return response;
            }
            
            // 获取用户信息
            Query userQuery = new Query(Criteria.where("username").is(username));
            User user = mongoTemplate.findOne(userQuery, User.class, USERS_COLLECTION);
            
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return response;
            }
            
            // 验证当前密码
            if (!argon2.verify(user.getPassword(), currentPassword.toCharArray())) {
                response.put("success", false);
                response.put("message", "当前密码不正确");
                return response;
            }
            
            // 更新密码
            String hashedPassword = argon2.hash(2, 65536, 1, newPassword.toCharArray());
            Update update = new Update().set("password", hashedPassword);
            mongoTemplate.updateFirst(userQuery, update, USERS_COLLECTION);
            
            // 发送通知邮件
            if (user.getEmail() != null) {
                emailService.sendPasswordChangeNotification(user.getEmail(), user.getUsername());
            }
            
            response.put("success", true);
            response.put("message", "密码修改成功");
            logger.info("用户 {} 密码修改成功", username);
            
        } catch (Exception e) {
            logger.error("修改密码时发生错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "系统错误，请稍后重试");
        }
        
        return response;
    }
}
