package com.tcmp2.service.Impl;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.tcmp2.pojo.entity.Material;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.COSFileService;
import com.tcmp2.service.DataCURDService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.lang.NonNull;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcmp2.pojo.entity.CustomProperty;

/**
 * <AUTHOR> Zhang Chengwei
 * @createDate : 2025-02-18 23:58
 * @description : 材料数据CRUD服务实现类
 */
@Service
public class DataCURDServiceImpl implements DataCURDService {

    private static final Logger logger = LoggerFactory.getLogger(DataCURDServiceImpl.class);
    private static final String MATERIALS_COLLECTION = "materials";

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AuthorizationService authorizationService;

    @Resource
    private COSFileService cosFileService;

    @Override
    public Map<String, Object> getMaterialById(String materialId) {
        logger.info("Searching for material with ID: {}", materialId);
        
        try {
            // 使用id字段而不是_id
            Query query = new Query(Criteria.where("id").is(materialId));

            // 使用Material实体类来确保类型安全
            Material material = mongoTemplate.findOne(query, Material.class, MATERIALS_COLLECTION);

            if (material == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("error", "Material not found");
                return response;
            }

            // 将Material对象转换为Map返回，保持API兼容性
            Map<String, Object> response = new HashMap<>();
            response.put("id", material.getId());
            response.put("Formula", material.getFormula());
            response.put("Space_group_sym", material.getSpaceGroupSym());
            response.put("Space_group_num", material.getSpaceGroupNum());
            response.put("Mag_ion_V", material.getMagIonV());
            response.put("Mag_ion_m", material.getMagIonM());
            response.put("Nm_Rln_2J_1_", material.getNmRln2J1());
            response.put("Structure", material.getStructure());
            response.put("magnetic_lattice", material.getMagneticLattice());
            response.put("createdBy", material.getCreatedBy());
            response.put("createdAt", material.getCreatedAt());
            response.put("lastModifiedBy", material.getLastModifiedBy());
            response.put("lastModifiedAt", material.getLastModifiedAt());

            // 添加其他重要字段
            response.put("a", material.getA());
            response.put("b", material.getB());
            response.put("c", material.getC());
            response.put("Point_group", material.getPointGroup());
            response.put("Crystal_system", material.getCrystalSystem());
            response.put("Mag_ion", material.getMagIon());
            response.put("Magnetic_moment", material.getMagneticMoment());
            response.put("T_N", material.getTN());
            response.put("DOI", material.getDoi());

            // 处理自定义属性
            if (material.getCustomProperties() != null) {
                response.put("customProperties", material.getCustomProperties());
            }

            // 处理其他动态字段
            if (material.getProperties() != null) {
                response.putAll(material.getProperties());
            }

            return response;
        } catch (org.springframework.dao.DataAccessException e) {
            logger.error("Database access error while fetching material: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("error", "数据库访问失败，请稍后重试");
            return response;
        } catch (Exception e) {
            logger.error("Unexpected error while fetching material: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("error", "获取材料信息失败，请稍后重试");
            return response;
        }
    }

    @Override
    public Map<String, Object> addMaterial(Map<String, Object> data, String currentUser) {
        logger.info("Adding new material with data: {} by user: {}", data, currentUser);

        Map<String, Object> response = new HashMap<>();

        if (data == null || data.isEmpty()) {
            logger.warn("No data provided for adding material");
            response.put("error", "No data provided");
            return response;
        }

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for adding material");
            response.put("error", "User authentication required");
            return response;
        }

        // 检查是否已经存在相同ID的材料
        String materialId = (String) data.get("id");

        // 处理自定义属性
        processCustomProperties(data);

        // 添加创建者信息和时间戳
        data.put("createdBy", currentUser);
        data.put("createdAt", new Date());
        data.put("lastModifiedBy", currentUser);
        data.put("lastModifiedAt", new Date());

        try {
            // 使用insert操作，如果ID重复会抛出异常，避免竞态条件
            mongoTemplate.insert(data, MATERIALS_COLLECTION);
        } catch (org.springframework.dao.DuplicateKeyException e) {
            logger.warn("Material with ID {} already exists", materialId);
            response.put("error", "Material with this ID already exists");
            return response;
        }
        logger.info("Material added successfully with ID: {} by user: {}", materialId, currentUser);
        response.put("message", "Material added successfully");
        return response;
    }

    @Override
    public Map<String, Object> updateMaterial(String materialId, Map<String, Object> data, String currentUser) {
        logger.info("Updating material with ID: {} and data: {} by user: {}", materialId, data, currentUser);

        Map<String, Object> response = new HashMap<>();

        if (data == null || data.isEmpty()) {
            logger.warn("No data provided for updating material");
            response.put("error", "No data provided");
            return response;
        }

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for updating material");
            response.put("error", "User authentication required");
            return response;
        }

        // 首先获取现有材料以检查权限
        Query query = new Query(Criteria.where("id").is(materialId));
        Material existingMaterial = mongoTemplate.findOne(query, Material.class, MATERIALS_COLLECTION);

        if (existingMaterial == null) {
            logger.warn("Material not found with ID: {}", materialId);
            response.put("error", "Material not found");
            return response;
        }

        // 检查用户是否有权限修改此材料
        if (!authorizationService.canModifyMaterial(currentUser, existingMaterial)) {
            logger.warn("User {} does not have permission to modify material {}", currentUser, materialId);
            response.put("error", "You do not have permission to modify this material");
            return response;
        }

        // 过滤掉不应该被修改的字段
        data.remove("createdBy");
        data.remove("createdAt");
        data.remove("lastModifiedBy");
        data.remove("lastModifiedAt");
        data.remove("_id"); // MongoDB的内部ID字段

        // 处理自定义属性
        processCustomProperties(data);

        // 添加最后修改者信息和时间戳
        data.put("lastModifiedBy", currentUser);
        data.put("lastModifiedAt", new Date());

        // 更新数据 - 特殊处理自定义属性的删除
        Update update = new Update();

        // 处理自定义属性的完整替换（包括删除）
        if (data.containsKey("customProperties")) {
            Object customProps = data.get("customProperties");
            if (customProps == null || (customProps instanceof Map && ((Map<?, ?>) customProps).isEmpty())) {
                // 如果自定义属性为空，则删除整个customProperties字段
                update.unset("customProperties");
                data.remove("customProperties"); // 从data中移除，避免重复处理
            } else {
                // 如果有自定义属性，则完整替换
                update.set("customProperties", customProps);
                data.remove("customProperties"); // 从data中移除，避免重复处理
            }
        }

        // 处理其他字段
        data.forEach(update::set);

        UpdateResult result = mongoTemplate.updateFirst(query, update, MATERIALS_COLLECTION);
        if (result.getMatchedCount() == 0) {
            logger.warn("Material not found with ID: {}", materialId);
            response.put("error", "Material not found");
            return response;
        }

        logger.info("Material updated successfully with ID: {} by user: {}", materialId, currentUser);
        response.put("message", "Material updated successfully");
        return response;
    }

    @Override
    public Map<String, Object> deleteMaterial(String materialId, String currentUser) {
        logger.info("Deleting material with ID: {} by user: {}", materialId, currentUser);

        Map<String, Object> response = new HashMap<>();

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for deleting material");
            response.put("error", "User authentication required");
            return response;
        }

        // 首先获取现有材料以检查权限
        Query query = new Query(Criteria.where("id").is(materialId));
        Material existingMaterial = mongoTemplate.findOne(query, Material.class, MATERIALS_COLLECTION);

        if (existingMaterial == null) {
            logger.warn("Material not found with ID: {}", materialId);
            response.put("error", "Material not found");
            return response;
        }

        // 检查用户是否有权限删除此材料
        if (!authorizationService.canDeleteMaterial(currentUser, existingMaterial)) {
            logger.warn("User {} does not have permission to delete material {}", currentUser, materialId);
            response.put("error", "You do not have permission to delete this material");
            return response;
        }

        // 删除数据库记录
        DeleteResult result = mongoTemplate.remove(query, MATERIALS_COLLECTION);

        if (result.getDeletedCount() == 0) {
            logger.warn("Material not found with ID: {}", materialId);
            response.put("error", "Material not found");
            return response;
        }

        // 删除对应的CIF文件
        String formula = existingMaterial.getFormula();
        if (formula != null && !formula.trim().isEmpty()) {
            boolean cifDeleted = cosFileService.deleteCIFFile(formula);
            if (cifDeleted) {
                logger.info("CIF file deleted successfully for material: {}", formula);
            } else {
                logger.warn("Failed to delete CIF file for material: {} (file may not exist)", formula);
            }
        }

        logger.info("Material deleted successfully with ID: {} by user: {}", materialId, currentUser);
        response.put("message", "Material deleted successfully");
        return response;
    }

    @Override
    public Map<String, Object> deleteAllMaterials(String currentUser) {
        logger.info("Deleting all materials by user: {}", currentUser);

        Map<String, Object> response = new HashMap<>();

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for deleting all materials");
            response.put("error", "User authentication required");
            return response;
        }

        // 只有管理员可以删除所有材料
        if (!authorizationService.isAdmin(currentUser)) {
            logger.warn("User {} does not have permission to delete all materials", currentUser);
            response.put("error", "Only administrators can delete all materials");
            return response;
        }

        try {
            // 使用原子操作：先查询并立即删除，避免竞态条件
            Query query = new Query();
            query.fields().include("Formula").include("id");

            // 使用findAndRemove确保原子性，避免在查询和删除之间有新数据插入
            List<Material> materialsToDelete = new ArrayList<>();

            // 批量删除，每次删除一批材料以避免长时间锁定
            int batchSize = 100;
            int totalDeleted = 0;

            do {
                Query batchQuery = new Query();
                batchQuery.fields().include("Formula").include("id");
                batchQuery.limit(batchSize);

                List<Material> batch = mongoTemplate.find(batchQuery, Material.class, MATERIALS_COLLECTION);
                if (batch.isEmpty()) {
                    break;
                }

                // 记录这批要删除的材料
                materialsToDelete.addAll(batch);

                // 删除这批材料
                List<String> idsToDelete = batch.stream()
                    .map(Material::getId)
                    .collect(Collectors.toList());

                Query deleteQuery = new Query(Criteria.where("id").in(idsToDelete));
                DeleteResult batchResult = mongoTemplate.remove(deleteQuery, MATERIALS_COLLECTION);
                totalDeleted += batchResult.getDeletedCount();

                logger.info("Deleted batch of {} materials, total deleted so far: {}",
                           batchResult.getDeletedCount(), totalDeleted);

            } while (true);

            logger.info("Found {} materials to delete CIF files for", materialsToDelete.size());

            // 删除对应的CIF文件（在数据库删除之后，避免文件删除失败影响数据库操作）
            int deletedCifCount = 0;
            int failedCifCount = 0;

            for (Material mat : materialsToDelete) {
                String formula = mat.getFormula();
                if (formula != null && !formula.trim().isEmpty()) {
                    try {
                        boolean cifDeleted = cosFileService.deleteCIFFile(formula);
                        if (cifDeleted) {
                            deletedCifCount++;
                            logger.debug("CIF file deleted successfully for material: {}", formula);
                        } else {
                            failedCifCount++;
                            logger.warn("Failed to delete CIF file for material: {} (file may not exist)", formula);
                        }
                    } catch (Exception e) {
                        failedCifCount++;
                        logger.error("Error deleting CIF file for material {}: {}", formula, e.getMessage());
                    }
                }
            }

            logger.info("CIF file deletion summary: {} deleted, {} failed", deletedCifCount, failedCifCount);

            response.put("message", "全部材料数据删除成功");
            response.put("deleted_count", totalDeleted);
            response.put("cif_deleted_count", deletedCifCount);
            response.put("cif_failed_count", failedCifCount);
        } catch (Exception e) {
            logger.error("Error while deleting materials: {}", e.getMessage(), e);
            response.put("error", e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addMaterialWithCIF(Map<String, Object> data, MultipartFile cifFile, String currentUser) {
        logger.info("Adding new material with CIF file by user: {}", currentUser);

        Map<String, Object> response = new HashMap<>();

        if (data == null || data.isEmpty()) {
            logger.warn("No data provided for adding material");
            response.put("error", "No data provided");
            return response;
        }

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for adding material");
            response.put("error", "User authentication required");
            return response;
        }

        // 获取材料ID用于后续操作
        String materialId = (String) data.get("id");

        // 如果有CIF文件，先上传到COS
        String cifFilePath = null;
        if (cifFile != null && !cifFile.isEmpty()) {
            String formula = (String) data.get("Formula");
            if (formula == null || formula.trim().isEmpty()) {
                logger.warn("Formula is required when uploading CIF file");
                response.put("error", "Formula is required when uploading CIF file");
                return response;
            }

            cifFilePath = cosFileService.uploadCIFFile(cifFile, formula);
            if (cifFilePath == null) {
                logger.error("Failed to upload CIF file for material: {}", materialId);
                response.put("error", "Failed to upload CIF file");
                return response;
            }

            // 将CIF文件路径添加到材料数据中
            data.put("cifFilePath", cifFilePath);
            logger.info("CIF file uploaded successfully: {}", cifFilePath);
        }

        // 处理自定义属性
        processCustomProperties(data);

        // 添加创建者信息和时间戳
        data.put("createdBy", currentUser);
        data.put("createdAt", new Date());
        data.put("lastModifiedBy", currentUser);
        data.put("lastModifiedAt", new Date());

        try {
            // 使用insert操作，如果ID重复会抛出异常，避免竞态条件
            mongoTemplate.insert(data, MATERIALS_COLLECTION);
            response.put("message", "Material added successfully");
            if (cifFilePath != null) {
                response.put("cifFilePath", cifFilePath);
            }
        } catch (org.springframework.dao.DuplicateKeyException e) {
            // 如果ID重复，安全地删除已上传的CIF文件
            if (cifFilePath != null) {
                String formula = (String) data.get("Formula");
                try {
                    cosFileService.deleteCIFFile(formula);
                    logger.info("Successfully deleted CIF file for duplicate material: {}", formula);
                } catch (Exception deleteError) {
                    logger.error("Failed to delete CIF file for duplicate material: {}", formula, deleteError);
                    // 记录清理失败，但不影响主要的错误响应
                    // 可以考虑添加到清理队列进行后续处理
                }
            }
            logger.warn("Material with ID {} already exists", materialId);
            response.put("error", "Material with this ID already exists");
        } catch (Exception e) {
            // 如果数据库插入失败，安全地删除已上传的CIF文件
            if (cifFilePath != null) {
                String formula = (String) data.get("Formula");
                try {
                    cosFileService.deleteCIFFile(formula);
                    logger.info("Successfully deleted CIF file after database failure: {}", formula);
                } catch (Exception deleteError) {
                    logger.error("Failed to delete CIF file after database failure: {}", formula, deleteError);
                    // 记录清理失败，但不影响主要的错误响应
                    // 可以考虑添加到清理队列进行后续处理
                }
            }
            logger.error("Error while adding material: {}", e.getMessage(), e);
            response.put("error", "Failed to add material: " + e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateMaterialWithCIF(String materialId, Map<String, Object> data, MultipartFile cifFile, String currentUser) {
        logger.info("Updating material with CIF file, ID: {} by user: {}", materialId, currentUser);

        Map<String, Object> response = new HashMap<>();

        if (data == null || data.isEmpty()) {
            logger.warn("No data provided for updating material");
            response.put("error", "No data provided");
            return response;
        }

        if (currentUser == null || currentUser.trim().isEmpty()) {
            logger.warn("No current user provided for updating material");
            response.put("error", "User authentication required");
            return response;
        }

        // 首先获取现有材料以检查权限
        Query query = new Query(Criteria.where("id").is(materialId));
        Material existingMaterial = mongoTemplate.findOne(query, Material.class, MATERIALS_COLLECTION);

        if (existingMaterial == null) {
            logger.warn("Material not found with ID: {}", materialId);
            response.put("error", "Material not found");
            return response;
        }

        // 检查用户是否有权限修改此材料
        if (!authorizationService.canModifyMaterial(currentUser, existingMaterial)) {
            logger.warn("User {} does not have permission to modify material {}", currentUser, materialId);
            response.put("error", "You do not have permission to modify this material");
            return response;
        }

        // 处理CIF文件更新
        String newCifFilePath = null;
        if (cifFile != null && !cifFile.isEmpty()) {
            String oldFormula = existingMaterial.getFormula();
            String newFormula = (String) data.get("Formula");

            // 如果没有提供新的Formula，使用原有的Formula
            if (newFormula == null || newFormula.trim().isEmpty()) {
                newFormula = oldFormula;
            }

            if (newFormula == null || newFormula.trim().isEmpty()) {
                logger.warn("Formula is required when uploading CIF file");
                response.put("error", "Formula is required when uploading CIF file");
                return response;
            }

            newCifFilePath = cosFileService.updateCIFFile(cifFile, oldFormula, newFormula);
            if (newCifFilePath == null) {
                logger.error("Failed to update CIF file for material: {}", materialId);
                response.put("error", "Failed to update CIF file");
                return response;
            }

            // 将新的CIF文件路径添加到更新数据中
            data.put("cifFilePath", newCifFilePath);
            logger.info("CIF file updated successfully: {}", newCifFilePath);
        }

        // 过滤掉不应该被修改的字段
        data.remove("createdBy");
        data.remove("createdAt");
        data.remove("lastModifiedBy");
        data.remove("lastModifiedAt");
        data.remove("_id"); // MongoDB的内部ID字段

        // 处理自定义属性
        processCustomProperties(data);

        // 添加最后修改者信息和时间戳
        data.put("lastModifiedBy", currentUser);
        data.put("lastModifiedAt", new Date());

        try {
            // 更新数据 - 特殊处理自定义属性的删除
            Update update = new Update();

            // 处理自定义属性的完整替换（包括删除）
            if (data.containsKey("customProperties")) {
                Object customProps = data.get("customProperties");
                if (customProps == null || (customProps instanceof Map && ((Map<?, ?>) customProps).isEmpty())) {
                    // 如果自定义属性为空，则删除整个customProperties字段
                    update.unset("customProperties");
                    data.remove("customProperties"); // 从data中移除，避免重复处理
                } else {
                    // 如果有自定义属性，则完整替换
                    update.set("customProperties", customProps);
                    data.remove("customProperties"); // 从data中移除，避免重复处理
                }
            }

            // 处理其他字段
            data.forEach(update::set);

            UpdateResult result = mongoTemplate.updateFirst(query, update, MATERIALS_COLLECTION);
            if (result.getMatchedCount() == 0) {
                logger.warn("Material not found with ID: {}", materialId);
                response.put("error", "Material not found");
                return response;
            }

            logger.info("Material with CIF file updated successfully with ID: {} by user: {}", materialId, currentUser);
            response.put("message", "Material updated successfully");
            if (newCifFilePath != null) {
                response.put("cifFilePath", newCifFilePath);
            }
        } catch (org.springframework.dao.DataAccessException e) {
            logger.error("Database access error while updating material: {}", e.getMessage(), e);
            response.put("error", "数据库更新失败，请稍后重试");
        } catch (Exception e) {
            logger.error("Unexpected error while updating material: {}", e.getMessage(), e);
            response.put("error", "更新材料信息失败，请稍后重试");
        }

        return response;
    }

    @Override
    public Map<String, Object> batchUploadFromZip(MultipartFile zipFile, String currentUser) {
        logger.info("开始处理ZIP批量上传，用户: {}, 文件: {}", currentUser, zipFile.getOriginalFilename());

        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> successes = new ArrayList<>();
        List<Map<String, Object>> failures = new ArrayList<>();

        try {
            // 检查ZIP文件大小，防止内存溢出
            long maxZipSize = 100 * 1024 * 1024; // 100MB限制
            if (zipFile.getSize() > maxZipSize) {
                result.put("error", "ZIP文件过大，请确保文件小于100MB");
                return result;
            }

            // 解析ZIP文件 - 使用流式处理，避免一次性加载所有文件到内存
            Map<String, byte[]> files = new HashMap<>();
            int maxFiles = 1000; // 限制文件数量
            int fileCount = 0;

            try (ZipInputStream zipInputStream = new ZipInputStream(zipFile.getInputStream())) {
                ZipEntry entry;

                while ((entry = zipInputStream.getNextEntry()) != null) {
                    if (fileCount >= maxFiles) {
                        logger.warn("ZIP文件包含过多文件，已达到限制: {}", maxFiles);
                        break;
                    }

                    if (!entry.isDirectory()) {
                        // 检查单个文件大小
                        long maxFileSize = 10 * 1024 * 1024; // 10MB限制
                        long entrySize = entry.getSize();

                        // 处理ZIP条目大小未知的情况
                        if (entrySize == -1) {
                            logger.warn("ZIP条目大小未知，将在读取时检查: {}", entry.getName());
                        } else if (entrySize > maxFileSize) {
                            logger.warn("跳过过大的文件: {} ({}字节)", entry.getName(), entrySize);
                            zipInputStream.closeEntry();
                            continue;
                        }

                        // 使用更大的缓冲区提高性能
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        byte[] buffer = new byte[8192]; // 增大缓冲区
                        int len;
                        long totalRead = 0;

                        while ((len = zipInputStream.read(buffer)) > 0) {
                            totalRead += len;
                            // 检查读取的数据大小，防止内存溢出
                            if (totalRead > maxFileSize) {
                                logger.warn("文件 {} 在读取过程中超过大小限制，跳过", entry.getName());
                                break;
                            }
                            baos.write(buffer, 0, len);
                        }

                        if (totalRead <= maxFileSize) {
                            files.put(entry.getName(), baos.toByteArray());
                            fileCount++;
                        }

                        // 及时释放内存
                        baos.close();
                    }
                    zipInputStream.closeEntry();
                }
            }

            logger.info("ZIP文件解析完成，共找到 {} 个文件", files.size());

            // 查找材料文件夹（包含data.json的文件夹）
            Map<String, MaterialFolder> materialFolders = findMaterialFolders(files);
            logger.info("找到 {} 个材料文件夹", materialFolders.size());

            if (materialFolders.isEmpty()) {
                result.put("error", "ZIP文件中未找到有效的材料数据。请确保每个材料文件夹包含data.json文件。");
                return result;
            }

            // 处理每个材料文件夹
            for (MaterialFolder folder : materialFolders.values()) {
                try {
                    logger.info("处理材料文件夹: {}", folder.folderName);

                    // 解析data.json
                    String jsonContent = new String(files.get(folder.dataJsonPath), StandardCharsets.UTF_8);
                    ObjectMapper objectMapper = new ObjectMapper();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> materialData = objectMapper.readValue(jsonContent, Map.class);

                    // 验证必填字段
                    if (!materialData.containsKey("Formula") || materialData.get("Formula") == null) {
                        throw new RuntimeException("data.json中缺少必填字段：Formula");
                    }

                    if (!materialData.containsKey("id") || materialData.get("id") == null) {
                        throw new RuntimeException("data.json中缺少必填字段：id");
                    }

                    // 查找CIF文件
                    String cifPath = findBestCifFile(folder, materialData.get("Formula").toString());
                    if (cifPath == null) {
                        throw new RuntimeException("文件夹中未找到CIF文件");
                    }

                    // 创建CIF文件对象
                    byte[] cifContent = files.get(cifPath);
                    String cifFileName = cifPath.substring(cifPath.lastIndexOf('/') + 1);
                    MultipartFile cifFile = new SimpleMultipartFile(
                        "cifFile",
                        cifFileName,
                        "chemical/x-cif",
                        cifContent
                    );

                    // 调用现有的添加方法
                    Map<String, Object> addResult = addMaterialWithCIF(materialData, cifFile, currentUser);

                    if (addResult.containsKey("error")) {
                        throw new RuntimeException(addResult.get("error").toString());
                    }

                    // 记录成功
                    Map<String, Object> success = new HashMap<>();
                    success.put("id", materialData.get("id"));
                    success.put("formula", materialData.get("Formula"));
                    success.put("folderName", folder.folderName);
                    successes.add(success);

                    logger.info("材料 {} 上传成功", materialData.get("id"));

                } catch (Exception e) {
                    logger.error("处理材料文件夹 {} 时发生错误: {}", folder.folderName, e.getMessage());

                    // 记录失败
                    Map<String, Object> failure = new HashMap<>();
                    failure.put("folderName", folder.folderName);
                    failure.put("filePath", folder.dataJsonPath);
                    failure.put("errorType", getErrorType(e));
                    failure.put("errorMessage", e.getMessage());
                    failure.put("suggestion", getErrorSuggestion(e));
                    failures.add(failure);
                }
            }

            // 构建结果
            result.put("totalCount", materialFolders.size());
            result.put("successCount", successes.size());
            result.put("failureCount", failures.size());
            result.put("successes", successes);
            result.put("failures", failures);
            result.put("hasErrors", !failures.isEmpty());

            logger.info("ZIP批量上传完成，成功: {}, 失败: {}", successes.size(), failures.size());

        } catch (Exception e) {
            logger.error("ZIP批量上传处理失败: {}", e.getMessage(), e);
            result.put("error", "ZIP文件处理失败: " + e.getMessage());
        }

        return result;
    }

    // 简单的MultipartFile实现
    private static class SimpleMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public SimpleMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name != null ? name : "";
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content == null ? 0 : content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content != null ? content : new byte[0];
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content != null ? content : new byte[0]);
        }

        @Override
        public void transferTo(@NonNull File dest) throws IOException, IllegalStateException {
            throw new UnsupportedOperationException("transferTo not supported");
        }
    }

    // 材料文件夹内部类
    private static class MaterialFolder {
        String folderName;
        String folderPath;
        String dataJsonPath;
        List<String> cifFiles;

        MaterialFolder(String folderName, String folderPath, String dataJsonPath) {
            this.folderName = folderName;
            this.folderPath = folderPath;
            this.dataJsonPath = dataJsonPath;
            this.cifFiles = new ArrayList<>();
        }
    }

    // 查找ZIP文件中的材料文件夹
    private Map<String, MaterialFolder> findMaterialFolders(Map<String, byte[]> files) {
        Map<String, MaterialFolder> folders = new HashMap<>();

        // 查找包含data.json的文件夹
        for (String filePath : files.keySet()) {
            if (filePath.endsWith("data.json")) {
                String folderPath = filePath.substring(0, filePath.lastIndexOf('/'));
                String folderName = folderPath.contains("/") ?
                    folderPath.substring(folderPath.lastIndexOf('/') + 1) : folderPath;

                MaterialFolder folder = new MaterialFolder(folderName, folderPath, filePath);
                folders.put(folderPath, folder);
            }
        }

        // 为每个文件夹查找CIF文件
        for (String filePath : files.keySet()) {
            if (filePath.endsWith(".cif")) {
                String folderPath = filePath.substring(0, filePath.lastIndexOf('/'));
                MaterialFolder folder = folders.get(folderPath);
                if (folder != null) {
                    folder.cifFiles.add(filePath);
                }
            }
        }

        return folders;
    }

    // 查找最佳匹配的CIF文件
    private String findBestCifFile(MaterialFolder folder, String formula) {
        if (folder.cifFiles.isEmpty()) {
            return null;
        }

        // 首先查找与Formula匹配的CIF文件
        String expectedCifName = formula + ".cif";
        for (String cifPath : folder.cifFiles) {
            if (cifPath.endsWith(expectedCifName)) {
                return cifPath;
            }
        }

        // 如果没有匹配的，返回第一个CIF文件
        return folder.cifFiles.get(0);
    }

    // 获取错误类型
    private String getErrorType(Exception error) {
        String message = error.getMessage();
        if (message.contains("ID重复") || message.contains("already exists")) {
            return "ID重复";
        } else if (message.contains("Formula")) {
            return "字段缺失";
        } else if (message.contains("CIF")) {
            return "CIF文件问题";
        } else if (message.contains("JSON")) {
            return "JSON格式错误";
        } else {
            return "未知错误";
        }
    }

    // 获取错误建议
    private String getErrorSuggestion(Exception error) {
        String message = error.getMessage();
        if (message.contains("ID重复") || message.contains("already exists")) {
            return "请修改材料ID，确保唯一性";
        } else if (message.contains("Formula")) {
            return "请在data.json中添加Formula字段";
        } else if (message.contains("CIF")) {
            return "请确保文件夹中包含有效的CIF文件";
        } else if (message.contains("JSON")) {
            return "请检查JSON文件格式是否正确";
        } else {
            return "请检查文件结构和数据格式";
        }
    }

    /**
     * 处理自定义属性，将前端发送的Map格式转换为CustomProperty对象
     */
    @SuppressWarnings("unchecked")
    private void processCustomProperties(Map<String, Object> data) {
        Object customPropsObj = data.get("customProperties");

        // 如果customProperties字段不存在，不做任何处理
        if (!data.containsKey("customProperties")) {
            return;
        }

        // 如果customProperties为null或空，保留这个状态（用于删除）
        if (customPropsObj == null) {
            logger.info("Custom properties is null, will be removed from database");
            return;
        }

        try {
            if (customPropsObj instanceof Map) {
                Map<String, Object> customPropsMap = (Map<String, Object>) customPropsObj;

                // 如果是空Map，保留这个状态（用于删除）
                if (customPropsMap.isEmpty()) {
                    logger.info("Custom properties is empty, will be removed from database");
                    return;
                }

                Map<String, CustomProperty> processedCustomProps = new HashMap<>();

                for (Map.Entry<String, Object> entry : customPropsMap.entrySet()) {
                    String propKey = entry.getKey();
                    Object propValue = entry.getValue();

                    if (propValue instanceof Map) {
                        Map<String, Object> propMap = (Map<String, Object>) propValue;

                        // 创建CustomProperty对象
                        CustomProperty customProperty = new CustomProperty();
                        customProperty.setValue(propMap.get("value"));
                        customProperty.setType((String) propMap.getOrDefault("type", "string"));
                        customProperty.setUnit((String) propMap.getOrDefault("unit", ""));
                        customProperty.setDescription((String) propMap.getOrDefault("description", ""));

                        // 验证自定义属性
                        if (customProperty.isValid()) {
                            processedCustomProps.put(propKey, customProperty);
                            logger.debug("Processed custom property: {} = {}", propKey, customProperty.getValue());
                        } else {
                            logger.warn("Invalid custom property: {}", propKey);
                        }
                    }
                }

                // 更新数据中的自定义属性
                if (!processedCustomProps.isEmpty()) {
                    data.put("customProperties", processedCustomProps);
                    logger.info("Processed {} custom properties", processedCustomProps.size());
                } else {
                    // 如果处理后没有有效的自定义属性，设置为空Map（用于删除）
                    data.put("customProperties", new HashMap<>());
                    logger.info("No valid custom properties found, will be removed from database");
                }
            } else {
                logger.warn("Custom properties is not a Map, removing from data");
                data.put("customProperties", new HashMap<>());
            }
        } catch (Exception e) {
            logger.error("Error processing custom properties: {}", e.getMessage(), e);
            // 如果处理失败，设置为空Map（用于删除）
            data.put("customProperties", new HashMap<>());
        }
    }
}
