package com.tcmp2.service.Impl;

import com.tcmp2.pojo.entity.Material;
import com.tcmp2.repository.MaterialRepository;
import com.tcmp2.service.SearchService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-18 0:23
 * @description :
 */
@Service
public class SearchServiceImpl implements SearchService {
    private static final Logger logger = LoggerFactory.getLogger(SearchServiceImpl.class);

    @Resource
    private MaterialRepository materialRepository;
    @Resource
    private MongoTemplate mongoTemplate;


    private static final Set<String> ELEMENTS = Set.of(
            "H", "He", "Li", "Be", "B", "C", "N", "O", "F", "Ne", "<PERSON>", "Mg", "<PERSON>", "<PERSON>", "P", "S", "Cl", "Ar", "K",
            "Ca", "Sc", "Ti", "V", "<PERSON>r", "Mn", "<PERSON>", "Co", "<PERSON>", "Cu", "Zn", "Ga", "Ge", "As", "Se", "Br", "Kr", "Rb",
            "Sr", "Y", "Zr", "Nb", "Mo", "Tc", "Ru", "Rh", "Pd", "Ag", "Cd", "In", "Sn", "Sb", "Te", "I", "Xe", "Cs",
            "Ba", "La", "Ce", "Pr", "Nd", "Pm", "Sm", "Eu", "Gd", "Tb", "Dy", "Ho", "Er", "Tm", "Yb", "Lu", "Hf", "Ta",
            "W", "Re", "Os", "Ir", "Pt", "Au", "Hg", "Tl", "Pb", "Bi", "Po", "At", "Rn", "Fr", "Ra", "Ac", "Th", "Pa",
            "U", "Np", "Pu", "Am", "Cm", "Bk", "Cf", "Es", "Fm", "Md", "No", "Lr", "Rf", "Db", "Sg", "Bh", "Hs", "Mt",
            "Ds", "Rg", "Cn", "Nh", "Fl", "Mc", "Lv", "Ts", "Og"
    );

    /**
     * 搜索材料
     *
     * @param params 查询参数
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> searchMaterials(Map<String, String> params) {
        logger.debug("Starting search with parameters: {}", params);

        // 处理分页参数
        int page = 1;
        int perPage = 10;
        try {
            if (params.containsKey("page")) {
                page = Math.max(1, Integer.parseInt(params.get("page")));
            }
            if (params.containsKey("per_page")) {
                perPage = Math.max(1, Math.min(100, Integer.parseInt(params.get("per_page")))); // 限制最大100条
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid pagination parameters, using defaults. page: {}, per_page: {}",
                       params.get("page"), params.get("per_page"));
        }

        logger.debug("Using pagination: page={}, per_page={}", page, perPage);

        // 创建查询对象
        Query query = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        // 存储需要后处理的过滤条件
        String[] elementsToInclude = null;
        String[] elementsToExclude = null;
        boolean needsElementFiltering = false;
        boolean needsExcludedElementFiltering = false;
        boolean isExactMatch = false; // 标记是否为精确匹配模式

        // 只查询主页需要的字段，避免查询大字段数据
        query.fields()
             .include("id")
             .include("Formula")
             .include("Space_group_sym")
             .include("Mag_ion_V")
             .include("Mag_ion_m")
             .include("Nm_Rln_2J_1_")
             .include("Structure") // Structure 用于元素匹配过滤，不直接显示
             .include("Space_group_num") // 用于空间群过滤
             .include("magnetic_lattice") // 用于磁晶格过滤
             .include("createdBy"); // 用于创建者过滤

        // 处理元素搜索 - 区分精确匹配和包含匹配
        if (params.containsKey("exact_elements")) {
            elementsToInclude = params.get("exact_elements").split("\\s+");
            needsElementFiltering = true;
            isExactMatch = true; // 精确匹配：元素组成必须完全一致（不多不少）
            logger.debug("Using exact match mode for elements: {}", Arrays.toString(elementsToInclude));
        } else if (params.containsKey("contains_elements")) {
            elementsToInclude = params.get("contains_elements").split("\\s+");
            needsElementFiltering = true;
            isExactMatch = false; // 包含匹配：必须包含所有指定元素，但可以有额外元素
            logger.debug("Using contains match mode for elements: {}", Arrays.toString(elementsToInclude));
        }

        // 处理排除元素搜索
        if (params.containsKey("exclude_elements")) {
            elementsToExclude = params.get("exclude_elements").split("\\s+");
            needsExcludedElementFiltering = true;
        }

        // 创建final变量用于lambda表达式
        final String[] finalElementsToInclude = elementsToInclude;
        final String[] finalElementsToExclude = elementsToExclude;
        final boolean finalIsExactMatch = isExactMatch;

        // 处理空间群号搜索 - 使用数据库索引优化
        if (params.containsKey("space_group_number")) {
            try {
                String[] numbers = params.get("space_group_number").split("[ ,，]+");
                List<Integer> spaceGroupNumbers = Arrays.stream(numbers)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

                if (!spaceGroupNumbers.isEmpty()) {
                    criteriaList.add(Criteria.where("Space_group_num").in(spaceGroupNumbers));
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid space group number format: {}", params.get("space_group_number"));
            }
        }

        // 处理磁晶格搜索 - 使用数据库索引优化
        if (params.containsKey("magnetic_lattice")) {
            String magneticLattice = params.get("magnetic_lattice");
            criteriaList.add(Criteria.where("magnetic_lattice").is(magneticLattice));
        }

        // 处理创建者过滤 - 使用数据库索引优化
        if (params.containsKey("createdBy")) {
            String createdBy = params.get("createdBy");
            criteriaList.add(Criteria.where("createdBy").is(createdBy));
        }

        // 构建最终查询 - 一次性查询，避免多次数据库访问
        if (!criteriaList.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // 优化分页处理：区分是否需要元素过滤
        List<Material> allFilteredMaterials;
        long totalCount;

        // 如果不需要元素过滤，直接使用数据库分页
        if (!needsElementFiltering && !needsExcludedElementFiltering) {
            // 先获取总数
            totalCount = mongoTemplate.count(query, Material.class);

            // 再获取分页数据
            query.skip((long) (page - 1) * perPage).limit(perPage);
            allFilteredMaterials = mongoTemplate.find(query, Material.class);
        } else {
            // 需要元素过滤时，先获取所有数据进行过滤
            List<Material> materials = mongoTemplate.find(query, Material.class);

            // 后处理：元素过滤（只有在需要时才进行）
            if (needsElementFiltering && finalElementsToInclude != null) {
                // 过滤出有效的元素
                String[] validElements = Arrays.stream(finalElementsToInclude)
                    .filter(ELEMENTS::contains)
                    .toArray(String[]::new);

                // 如果输入的元素都无效，返回空结果
                if (validElements.length == 0) {
                    logger.debug("No valid elements found in search query, returning empty result");
                    allFilteredMaterials = new ArrayList<>();
                } else {
                    if (finalIsExactMatch) {
                        // 精确匹配：材料的元素组成必须与指定元素完全一致（不多不少）
                        logger.debug("Applying exact match filter for elements: {}", Arrays.toString(validElements));
                        Set<String> validElementsSet = new HashSet<>(Arrays.asList(validElements));
                        allFilteredMaterials = materials.stream()
                            .filter(mat -> {
                                Set<String> structureElements = extractElements(mat.getStructure());
                                // 材料的元素集合必须与搜索的元素集合完全相等
                                return structureElements.equals(validElementsSet);
                            })
                            .collect(Collectors.toList());
                    } else {
                        // 模糊匹配：材料必须包含所有指定的元素，但可以有额外元素
                        logger.debug("Applying include match filter for elements: {}", Arrays.toString(validElements));
                        allFilteredMaterials = materials.stream()
                            .filter(mat -> {
                                Set<String> structureElements = extractElements(mat.getStructure());
                                return Arrays.stream(validElements)
                                    .allMatch(structureElements::contains);
                            })
                            .collect(Collectors.toList());
                    }
                }
            } else {
                allFilteredMaterials = materials;
            }

            // 处理排除元素过滤
            if (needsExcludedElementFiltering && finalElementsToExclude != null) {
                Set<String> excludedSet = Arrays.stream(finalElementsToExclude)
                    .filter(ELEMENTS::contains)
                    .collect(Collectors.toSet());

                allFilteredMaterials = allFilteredMaterials.stream()
                    .filter(mat -> {
                        Set<String> structureElements = extractElements(mat.getStructure());
                        return structureElements.stream().noneMatch(excludedSet::contains);
                    })
                    .collect(Collectors.toList());
            }

            // 计算总数
            totalCount = allFilteredMaterials.size();

            // 应用分页
            int startIndex = (page - 1) * perPage;
            int endIndex = Math.min(startIndex + perPage, allFilteredMaterials.size());

            if (startIndex >= allFilteredMaterials.size()) {
                // 页码超出范围，返回空列表
                allFilteredMaterials = new ArrayList<>();
            } else {
                allFilteredMaterials = allFilteredMaterials.subList(startIndex, endIndex);
            }
        }

        logger.debug("Pagination applied: total={}, page={}, per_page={}, returned={}",
                    totalCount, page, perPage, allFilteredMaterials.size());

        // 转换结果并返回
        List<Map<String, Object>> resultList = allFilteredMaterials.stream()
            .map(this::convertToMapForSearch)
            .collect(Collectors.toList());

        return Map.of(
            "count", totalCount,  // 总记录数，用于前端分页计算
            "materials", resultList,  // 当前页的数据
            "page", page,  // 当前页码
            "per_page", perPage,  // 每页条数
            "total_pages", (totalCount + perPage - 1) / perPage  // 总页数
        );
    }

    /**
     * 将Material对象转换为搜索结果Map（仅包含主页搜索需要的字段）
     * @param material Material对象
     * @return Map对象
     */
    private Map<String, Object> convertToMapForSearch(Material material) {
        Map<String, Object> result = new HashMap<>();
        
        // 只添加主页搜索结果表格中需要显示的字段
        result.put("id", material.getId());
        result.put("Formula", material.getFormula());
        result.put("Space_group_sym", material.getSpaceGroupSym());
        result.put("Mag_ion_V", material.getMagIonV());
        result.put("Mag_ion_m", material.getMagIonM());
        result.put("Nm_Rln_2J_1_", material.getNmRln2J1());
        // 添加magnetic_lattice字段用于前端筛选
        result.put("magnetic_lattice", material.getMagneticLattice());
        
        // 移除null值
        result.values().removeIf(Objects::isNull);
        
        return result;
    }

    /**
     * 从化学式中提取元素
     * @param structure 化学式
     * @return 元素集合
     */
    private Set<String> extractElements(String structure) {
        if (structure == null) return Collections.emptySet();
        
        Set<String> elements = new HashSet<>();
        
        // 首先处理括号内的内容，将括号展开
        String processedStructure = structure.replaceAll("\\(([^()]+)\\)\\d*", "$1");
        
        // 使用正则表达式匹配元素符号
        // 匹配规则：
        // 1. 大写字母开头
        // 2. 可选的小写字母
        // 3. 确保前后不是字母（避免Ba匹配到B）
        Pattern pattern = Pattern.compile("(?:^|[^a-zA-Z])([A-Z][a-z]?)(?:\\d|[^a-zA-Z]|$)");
        Matcher matcher = pattern.matcher(processedStructure);
        while (matcher.find()) {
            String element = matcher.group(1);
            if (ELEMENTS.contains(element)) {
                elements.add(element);
            }
        }
        
        return elements;
    }

    /**
     * 将Material对象转换为Map
     * @param material Material对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(Material material) {
        Map<String, Object> result = new HashMap<>();

        // 手动映射字段，使用id而不是mongoId
        result.put("id", material.getId());  // 使用id字段
        result.put("Structure", material.getStructure());
        result.put("Formula", material.getFormula());
        result.put("a", material.getA());
        result.put("b", material.getB());
        result.put("c", material.getC());
        result.put("a1", material.getA1());
        result.put("b1", material.getB1());
        result.put("c1", material.getC1());
        result.put("Space_group_num", material.getSpaceGroupNum());
        result.put("Space_group_sym", material.getSpaceGroupSym());
        result.put("Point_group", material.getPointGroup());
        result.put("Crystal_system", material.getCrystalSystem());
        result.put("magnetic_lattice", material.getMagneticLattice());
        result.put("Density", material.getDensity());
        result.put("V_unit_cell", material.getVUnitCell());
        result.put("Mole_Mass", material.getMoleMass());
        result.put("Mole_Volume", material.getMoleVolume());
        result.put("GGA_band_gap", material.getGGABandGap());
        result.put("E_hull", material.getEHull());
        result.put("Mag_ion", material.getMagIon());
        result.put("Mag_ion_V", material.getMagIonV());
        result.put("Mag_ion_m", material.getMagIonM());
        result.put("Magnetic_moment", material.getMagneticMoment());
        result.put("Effective_Spin", material.getEffectiveSpin());
        result.put("S_GS_Vol", material.getSGSVol());
        result.put("S_GS_Mass", material.getSGSMass());
        result.put("T_N", material.getTN());
        result.put("T_access", material.getTAccess());
        
        // 以下字段在后端处理但前端不显示
        result.put("Nm_Rln_2J_1_", material.getNmRln2J1());
        result.put("S_GS_mol", material.getSGSMol());
        result.put("S_GS_mol2", material.getSGSMol2());
        result.put("Nearest_Mag_Ion_Distance_In_plane", material.getNearestMagIonDistanceInPlane());
        result.put("Nearest_Mag_Ion_Distance_inter_plane", material.getNearestMagIonDistanceInterPlane());
        
        result.put("DOI", material.getDoi());

        // 添加创建者和修改者信息
        result.put("createdBy", material.getCreatedBy());
        result.put("createdAt", material.getCreatedAt());
        result.put("lastModifiedBy", material.getLastModifiedBy());
        result.put("lastModifiedAt", material.getLastModifiedAt());

        // 添加自定义属性
        if (material.getCustomProperties() != null && !material.getCustomProperties().isEmpty()) {
            result.put("customProperties", material.getCustomProperties());
        }

        // 移除null值
        result.values().removeIf(Objects::isNull);

        return result;
    }

    /**
     * 根据材料ID获取材料信息
     *
     * @param id 材料ID
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> getMaterialById(String id) {
        // 详情页查询需要所有字段，不使用投影
        Query query = new Query(Criteria.where("id").is(id));
        Material material = mongoTemplate.findOne(query, Material.class);
        
        if (material == null) {
            return null;
        }
        
        // 详情页查询，需要包含所有字段不包括cif和XRD数据
        return convertToMap(material);
    }

    /**
     * 根据材料ID获取化学式formula
     *
     * @param id
     * @return String
     */
    @Override
    public String getFormulaById(String id) {
        Query query = new Query(Criteria.where("id").is(id));
        Material material = mongoTemplate.findOne(query, Material.class);
        if (material == null) {
            logger.warn("Material not found with ID: {}", id);
            return null;
        }
        return material.getFormula();
    }
}
