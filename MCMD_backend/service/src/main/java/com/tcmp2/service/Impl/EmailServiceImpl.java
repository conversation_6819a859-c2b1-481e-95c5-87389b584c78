package com.tcmp2.service.Impl;

import com.tcmp2.service.EmailService;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 邮件服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 邮件发送服务实现
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);
    
    private final JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${app.frontend-url}")
    private String frontendUrl;
    
    public EmailServiceImpl(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }
    
    @Override
    public boolean sendPasswordResetEmail(String toEmail, String username, String resetLink) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setFrom(fromEmail, "MCMD系统");
            helper.setTo(toEmail);
            helper.setSubject("[MCMD] 密码重置验证");
            
            String htmlContent = buildPasswordResetEmailContent(username, resetLink);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            logger.info("密码重置邮件发送成功: {}", toEmail);
            return true;
            
        } catch (Exception e) {
            logger.error("发送密码重置邮件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean sendPasswordChangeNotification(String toEmail, String username) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setFrom(fromEmail, "MCMD系统");
            helper.setTo(toEmail);
            helper.setSubject("[MCMD] 密码修改成功通知");
            
            String htmlContent = buildPasswordChangeNotificationContent(username);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            logger.info("密码修改通知邮件发送成功: {}", toEmail);
            return true;
            
        } catch (Exception e) {
            logger.error("发送密码修改通知邮件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private String buildPasswordResetEmailContent(String username, String resetLink) {
        return """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>MCMD - 密码重置</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background-color: #ffffff;
                        border-radius: 12px;
                        padding: 40px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    .logo {
                        width: 64px;
                        height: 64px;
                        background-color: #1976d2;
                        border-radius: 50%;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #1976d2;
                        font-size: 24px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .subtitle {
                        color: #666;
                        font-size: 16px;
                        margin: 8px 0 0 0;
                    }
                    .content {
                        margin: 30px 0;
                    }
                    .greeting {
                        font-size: 18px;
                        margin-bottom: 20px;
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.8;
                        margin-bottom: 30px;
                    }
                    .button-container {
                        text-align: center;
                        margin: 30px 0;
                    }
                    .reset-button {
                        display: inline-block;
                        background-color: #1976d2;
                        color: white;
                        text-decoration: none;
                        padding: 14px 32px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 500;
                    }
                    .link-info {
                        background-color: #f8f9fa;
                        border-left: 4px solid #1976d2;
                        padding: 16px;
                        margin: 20px 0;
                        border-radius: 4px;
                    }
                    .link-text {
                        font-family: monospace;
                        font-size: 14px;
                        word-break: break-all;
                        color: #666;
                        margin: 8px 0;
                    }
                    .warning {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 6px;
                        padding: 16px;
                        margin: 20px 0;
                    }
                    .warning-title {
                        color: #856404;
                        font-weight: bold;
                        margin-bottom: 8px;
                    }
                    .warning-text {
                        color: #856404;
                        font-size: 14px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">M</div>
                        <h1 class="title">MCMD 密码重置</h1>
                        <p class="subtitle">Magnetocaloric Materials Database</p>
                    </div>
            
                    <div class="content">
                        <div class="greeting">
                            您好，%s！
                        </div>
            
                        <div class="message">
                            我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置您的密码：
                        </div>
            
                        <div class="button-container">
                            <a href="%s" class="reset-button">重置密码</a>
                        </div>
            
                        <div class="link-info">
                            <strong>如果按钮无法点击，请复制以下链接到浏览器地址栏：</strong>
                            <div class="link-text">%s</div>
                        </div>
            
                        <div class="warning">
                            <div class="warning-title">⚠️ 安全提醒</div>
                            <div class="warning-text">
                                • 此链接将在 <strong>30分钟</strong> 后失效<br>
                                • 如果您没有请求重置密码，请忽略此邮件<br>
                                • 为了您的账户安全，请不要将此链接分享给他人<br>
                                • 重置密码后，请妥善保管您的新密码
                            </div>
                        </div>
            
                        <div class="message">
                            如果您有任何疑问或需要帮助，请联系我们的技术支持团队。
                        </div>
                    </div>
            
                    <div class="footer">
                        <p>此邮件由 MCMD 系统自动发送，请勿直接回复。</p>
                        <p style="margin-top: 16px; color: #999; font-size: 12px;">
                            © 2024 MCMD@TCMP2. All rights reserved.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(username, resetLink, resetLink);
    }
    
    private String buildPasswordChangeNotificationContent(String username) {
        return """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>MCMD - 密码修改成功</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background-color: #ffffff;
                        border-radius: 12px;
                        padding: 40px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    .logo {
                        width: 64px;
                        height: 64px;
                        background-color: #4caf50;
                        border-radius: 50%;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #4caf50;
                        font-size: 24px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .subtitle {
                        color: #666;
                        font-size: 16px;
                        margin: 8px 0 0 0;
                    }
                    .content {
                        margin: 30px 0;
                    }
                    .greeting {
                        font-size: 18px;
                        margin-bottom: 20px;
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.8;
                        margin-bottom: 30px;
                    }
                    .success-info {
                        background-color: #f1f8e9;
                        border-left: 4px solid #4caf50;
                        padding: 16px;
                        margin: 20px 0;
                        border-radius: 4px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">✓</div>
                        <h1 class="title">密码修改成功</h1>
                        <p class="subtitle">MCMD - Magnetocaloric Materials Database</p>
                    </div>
            
                    <div class="content">
                        <div class="greeting">
                            您好，%s！
                        </div>
            
                        <div class="message">
                            您的MCMD账户密码已成功修改。
                        </div>
            
                        <div class="success-info">
                            <strong>修改时间：</strong> %s<br>
                            <strong>安全提醒：</strong> 如果这不是您本人的操作，请立即联系我们的技术支持团队。
                        </div>
            
                        <div class="message">
                            为了您的账户安全，请妥善保管您的新密码，不要与他人分享。
                        </div>
                    </div>
            
                    <div class="footer">
                        <p>此邮件由 MCMD 系统自动发送，请勿直接回复。</p>
                        <p style="margin-top: 16px; color: #999; font-size: 12px;">
                            © 2024 MCMD@TCMP2. All rights reserved.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(username, new java.util.Date().toString());
    }
}
