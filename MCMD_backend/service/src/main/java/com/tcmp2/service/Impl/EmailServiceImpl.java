package com.tcmp2.service.Impl;

import com.tcmp2.service.EmailService;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 邮件服务实现类
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 邮件发送服务实现
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);
    
    private final JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${app.frontend-url}")
    private String frontendUrl;
    
    public EmailServiceImpl(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }
    
    @Override
    public boolean sendPasswordResetEmail(String toEmail, String username, String resetLink) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setFrom(fromEmail, "MCMD System");
            helper.setTo(toEmail);
            helper.setSubject("[MCMD] Password Reset Verification");
            
            String htmlContent = buildPasswordResetEmailContent(username, resetLink);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            logger.info("密码重置邮件发送成功: {}", toEmail);
            return true;
            
        } catch (Exception e) {
            logger.error("发送密码重置邮件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean sendPasswordChangeNotification(String toEmail, String username) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, StandardCharsets.UTF_8.name());
            
            helper.setFrom(fromEmail, "MCMD System");
            helper.setTo(toEmail);
            helper.setSubject("[MCMD] Password Change Notification");
            
            String htmlContent = buildPasswordChangeNotificationContent(username);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            logger.info("密码修改通知邮件发送成功: {}", toEmail);
            return true;
            
        } catch (Exception e) {
            logger.error("发送密码修改通知邮件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private String buildPasswordResetEmailContent(String username, String resetLink) {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>MCMD - Password Reset</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background-color: #ffffff;
                        border-radius: 12px;
                        padding: 40px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    .logo {
                        width: 64px;
                        height: 64px;
                        background-color: #1976d2;
                        border-radius: 50%;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #1976d2;
                        font-size: 24px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .subtitle {
                        color: #666;
                        font-size: 16px;
                        margin: 8px 0 0 0;
                    }
                    .content {
                        margin: 30px 0;
                    }
                    .greeting {
                        font-size: 18px;
                        margin-bottom: 20px;
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.8;
                        margin-bottom: 30px;
                    }
                    .button-container {
                        text-align: center;
                        margin: 30px 0;
                    }
                    .reset-button {
                        display: inline-block;
                        background-color: #1976d2;
                        color: white;
                        text-decoration: none;
                        padding: 14px 32px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 500;
                    }
                    .link-info {
                        background-color: #f8f9fa;
                        border-left: 4px solid #1976d2;
                        padding: 16px;
                        margin: 20px 0;
                        border-radius: 4px;
                    }
                    .link-text {
                        font-family: monospace;
                        font-size: 14px;
                        word-break: break-all;
                        color: #666;
                        margin: 8px 0;
                    }
                    .warning {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 6px;
                        padding: 16px;
                        margin: 20px 0;
                    }
                    .warning-title {
                        color: #856404;
                        font-weight: bold;
                        margin-bottom: 8px;
                    }
                    .warning-text {
                        color: #856404;
                        font-size: 14px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">M</div>
                        <h1 class="title">MCMD Password Reset</h1>
                        <p class="subtitle">Magnetocaloric Materials Database</p>
                    </div>
            
                    <div class="content">
                        <div class="greeting">
                            Hello, %s!
                        </div>

                        <div class="message">
                            We received a password reset request for your account. If this was you, please click the button below to reset your password:
                        </div>

                        <div class="button-container">
                            <a href="%s" class="reset-button">Reset Password</a>
                        </div>

                        <div class="link-info">
                            <strong>If the button doesn't work, please copy and paste the following link into your browser:</strong>
                            <div class="link-text">%s</div>
                        </div>
            
                        <div class="warning">
                            <div class="warning-title">⚠️ Security Notice</div>
                            <div class="warning-text">
                                • This link will expire in <strong>30 minutes</strong><br>
                                • If you didn't request a password reset, please ignore this email<br>
                                • For your account security, do not share this link with others<br>
                                • After resetting your password, please keep your new password secure
                            </div>
                        </div>

                        <div class="message">
                            If you have any questions or need assistance, please contact our technical support team.
                        </div>
                    </div>
            
                    <div class="footer">
                        <p>This email was sent automatically by the MCMD system. Please do not reply directly.</p>
                        <p style="margin-top: 16px; color: #999; font-size: 12px;">
                            © %d MCMD@TCMP2. All rights reserved.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(username, resetLink, resetLink, java.time.Year.now().getValue());
    }
    
    private String buildPasswordChangeNotificationContent(String username) {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>MCMD - Password Changed Successfully</title>
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background-color: #ffffff;
                        border-radius: 12px;
                        padding: 40px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    .logo {
                        width: 64px;
                        height: 64px;
                        background-color: #4caf50;
                        border-radius: 50%;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #4caf50;
                        font-size: 24px;
                        font-weight: bold;
                        margin: 0;
                    }
                    .subtitle {
                        color: #666;
                        font-size: 16px;
                        margin: 8px 0 0 0;
                    }
                    .content {
                        margin: 30px 0;
                    }
                    .greeting {
                        font-size: 18px;
                        margin-bottom: 20px;
                    }
                    .message {
                        font-size: 16px;
                        line-height: 1.8;
                        margin-bottom: 30px;
                    }
                    .success-info {
                        background-color: #f1f8e9;
                        border-left: 4px solid #4caf50;
                        padding: 16px;
                        margin: 20px 0;
                        border-radius: 4px;
                    }
                    .footer {
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                        color: #666;
                        font-size: 14px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">✓</div>
                        <h1 class="title">Password Changed Successfully</h1>
                        <p class="subtitle">MCMD - Magnetocaloric Materials Database</p>
                    </div>
            
                    <div class="content">
                        <div class="greeting">
                            Hello, %s!
                        </div>

                        <div class="message">
                            Your MCMD account password has been successfully changed.
                        </div>

                        <div class="success-info">
                            <strong>Change Time:</strong> %s<br>
                            <strong>Security Notice:</strong> If this was not done by you, please contact our technical support team immediately.
                        </div>

                        <div class="message">
                            For your account security, please keep your new password safe and do not share it with others.
                        </div>
                    </div>
            
                    <div class="footer">
                        <p>This email was sent automatically by the MCMD system. Please do not reply directly.</p>
                        <p style="margin-top: 16px; color: #999; font-size: 12px;">
                            © %d MCMD@TCMP2. All rights reserved.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(username, new java.util.Date().toString(), java.time.Year.now().getValue());
    }

    @Override
    public boolean sendEmailVerification(String toEmail, String username, String token) {
        try {
            String verificationLink = "http://localhost:5173/verify-email?token=" + token;
            String subject = "MCMD Email Verification - Activate Your Account";
            String htmlContent = createEmailVerificationTemplate(username, verificationLink);

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom("<EMAIL>", "MCMD System");
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);

            mailSender.send(message);
            logger.info("邮箱验证邮件发送成功: {}", toEmail);
            return true;

        } catch (Exception e) {
            logger.error("发送邮箱验证邮件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    private String createEmailVerificationTemplate(String username, String verificationLink) {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>MCMD Email Verification</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 600px;
                        margin: 0 auto;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        overflow: hidden;
                    }
                    .header {
                        background: linear-gradient(135deg, #1976d2, #42a5f5);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }
                    .logo {
                        width: 60px;
                        height: 60px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%%;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 15px;
                    }
                    .title {
                        margin: 0 0 8px 0;
                        font-size: 28px;
                        font-weight: 600;
                    }
                    .subtitle {
                        margin: 0;
                        opacity: 0.9;
                        font-size: 14px;
                    }
                    .content {
                        padding: 40px 30px;
                    }
                    .greeting {
                        font-size: 18px;
                        font-weight: 600;
                        color: #1976d2;
                        margin-bottom: 20px;
                    }
                    .message {
                        margin-bottom: 30px;
                        font-size: 16px;
                        line-height: 1.6;
                    }
                    .button-container {
                        text-align: center;
                        margin: 35px 0;
                    }
                    .verify-button {
                        display: inline-block;
                        background: linear-gradient(135deg, #4caf50, #66bb6a);
                        color: white;
                        padding: 15px 35px;
                        text-decoration: none;
                        border-radius: 8px;
                        font-weight: 600;
                        font-size: 16px;
                        box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
                        transition: all 0.3s ease;
                    }
                    .verify-button:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
                    }
                    .link-info {
                        background: #f8f9fa;
                        border-left: 4px solid #1976d2;
                        padding: 20px;
                        margin: 25px 0;
                        border-radius: 0 8px 8px 0;
                    }
                    .link-text {
                        background: #e3f2fd;
                        padding: 12px;
                        border-radius: 6px;
                        font-family: monospace;
                        font-size: 14px;
                        word-break: break-all;
                        margin-top: 10px;
                        border: 1px solid #bbdefb;
                    }
                    .warning {
                        background: #fff3e0;
                        border: 1px solid #ffcc02;
                        border-radius: 8px;
                        padding: 20px;
                        margin: 25px 0;
                    }
                    .warning-title {
                        font-weight: 600;
                        color: #f57c00;
                        margin-bottom: 10px;
                        font-size: 16px;
                    }
                    .warning-text {
                        color: #e65100;
                        font-size: 14px;
                        line-height: 1.5;
                    }
                    .footer {
                        background: #f8f9fa;
                        padding: 25px 30px;
                        text-align: center;
                        color: #666;
                        font-size: 14px;
                        border-top: 1px solid #e9ecef;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="logo">M</div>
                        <h1 class="title">MCMD Email Verification</h1>
                        <p class="subtitle">Magnetocaloric Materials Database</p>
                    </div>

                    <div class="content">
                        <div class="greeting">
                            Welcome to MCMD, %s!
                        </div>

                        <div class="message">
                            Thank you for registering your MCMD account. To ensure your email address is valid and activate your account, please click the button below to complete email verification:
                        </div>

                        <div class="button-container">
                            <a href="%s" class="verify-button">Verify Email</a>
                        </div>

                        <div class="link-info">
                            <strong>If the button doesn't work, please copy and paste the following link into your browser:</strong>
                            <div class="link-text">%s</div>
                        </div>

                        <div class="warning">
                            <div class="warning-title">⚠️ Important Notice</div>
                            <div class="warning-text">
                                • This verification link will expire in <strong>24 hours</strong><br>
                                • After successful verification, your account will be activated and you can use all features<br>
                                • If you didn't register for an MCMD account, please ignore this email<br>
                                • For your account security, do not share this link with others
                            </div>
                        </div>

                        <div class="message">
                            After verification is complete, you will be able to use all MCMD features. If you have any questions, please contact our technical support team.
                        </div>
                    </div>

                    <div class="footer">
                        <p>This email was sent automatically by the MCMD system. Please do not reply directly.</p>
                        <p style="margin-top: 16px; color: #999; font-size: 12px;">
                            © %d MCMD@TCMP2. All rights reserved.
                        </p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(username, verificationLink, verificationLink);
    }
}
