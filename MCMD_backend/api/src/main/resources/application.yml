server:
  port: 8081
spring:
  data:
    mongodb:
      uri: ${MONGODB_URI:************************************************************}
      # 连接池配置
      options:
        max-connection-pool-size: 100
        min-connection-pool-size: 10
        max-connection-idle-time: 60000
        max-connection-life-time: 120000
        connect-timeout: 10000
        socket-timeout: 30000
        server-selection-timeout: 30000
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
  # 邮件配置
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: btcs eufa tomc jzry
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

# 腾讯云COS配置
tencent:
  cos:
    secretId: ${TENCENT_COS_SECRET_ID:AKIDpVxBBwVr1xfBXhfRvsrbAf2FIH7lC0b1}
    secretKey: ${TENCENT_COS_SECRET_KEY:FNJ1d096pJZaNwHOHySjmbBcw3yiFc6S}
    bucketName: ${TENCENT_COS_BUCKET_NAME:mat-database-zcw-1311407817}
    region: ${TENCENT_COS_REGION:ap-nanjing}
    domain: ${TENCENT_COS_DOMAIN:https://mat-database-zcw-1311407817.cos.ap-nanjing.myqcloud.com}

# CORS配置
cors:
  # 允许的源地址，多个地址用逗号分隔
  # 开发环境示例：http://localhost:8080,http://127.0.0.1:8080
  # 生产环境示例：https://yourdomain.com,https://www.yourdomain.com
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:8080,http://127.0.0.1:8080}

# Cookie配置
cookie:
  secure: false
  domain:

# JWT配置
jwt:
  secret: ${JWT_SECRET:MCMD_JWT_SECRET_KEY_FOR_MATERIAL_DATABASE_SYSTEM_2025_VERY_LONG_SECRET_KEY_TO_ENSURE_SECURITY}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时（毫秒）
  refresh-window: ${JWT_REFRESH_WINDOW:21600000} # 6小时（毫秒）

# 应用配置
app:
  frontend-url: ${FRONTEND_URL:http://localhost:8080}
  password-reset:
    token-expiry-minutes: ${PASSWORD_RESET_TOKEN_EXPIRY:30} # 密码重置token有效期（分钟）

# LangChain4J配置
langchain4j:
  open-ai:
    streaming-chat-model:
      api-key: ${LANGCHAIN4J_API_KEY:sk-c5d27384506c40e5a611f543340c5129}
      base-url: ${LANGCHAIN4J_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
      model-name: ${LANGCHAIN4J_MODEL_NAME:deepseek-v3}
      log-requests: true
      log-responses: true
      temperature: 0.7
    chat-model:
      api-key: ${LANGCHAIN4J_API_KEY:sk-c5d27384506c40e5a611f543340c5129}
      base-url: ${LANGCHAIN4J_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
      model-name: ${LANGCHAIN4J_MODEL_NAME:deepseek-v3}
      log-requests: true
      log-responses: true
      temperature: 0.7
#    streaming-chat-model:
#      api-key: ${LANGCHAIN4J_API_KEY:sk-DgeXYOjzAVCYeJF0LC1ev0dRpTcUqg6nn82ovJ7E8Exh7Iou}
#      base-url: ${LANGCHAIN4J_BASE_URL:https://api.lkeap.cloud.tencent.com/v1}
#      model-name: ${LANGCHAIN4J_MODEL_NAME:deepseek-v3-0324}
#      log-requests: true
#      log-responses: true
#      temperature: 0.7
#    chat-model:
#      api-key: ${LANGCHAIN4J_API_KEY:sk-DgeXYOjzAVCYeJF0LC1ev0dRpTcUqg6nn82ovJ7E8Exh7Iou}
#      base-url: ${LANGCHAIN4J_BASE_URL:https://api.lkeap.cloud.tencent.com/v1}
#      model-name: ${LANGCHAIN4J_MODEL_NAME:deepseek-v3-0324}
#      log-requests: true
#      log-responses: true
#      temperature: 0.7

# 日志配置
logging:
  level:
    com.tcmp2.exception.GlobalExceptionHandler: DEBUG
    com.tcmp2.controller.MaterialAnalysisController: DEBUG
    org.springframework.web.context.request.async: WARN
    org.apache.catalina.connector: WARN
