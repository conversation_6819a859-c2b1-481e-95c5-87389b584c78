package com.tcmp2.controller;

import com.tcmp2.service.EmailVerificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 邮箱验证控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 15:00
 * @description : 处理邮箱验证相关的API请求
 */
@RestController
@RequestMapping("/email-verification")
public class EmailVerificationController {

    private static final Logger logger = LoggerFactory.getLogger(EmailVerificationController.class);

    @Resource
    private EmailVerificationService emailVerificationService;

    /**
     * 验证邮箱
     */
    @GetMapping("/verify")
    public ResponseEntity<?> verifyEmail(@RequestParam String token) {
        logger.info("Received email verification request, token: {}", token.substring(0, Math.min(token.length(), 8)) + "...");

        Map<String, Object> result = emailVerificationService.verifyEmail(token);

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 重新发送验证邮件
     */
    @PostMapping("/resend")
    public ResponseEntity<?> resendVerificationEmail(@RequestBody Map<String, String> request) {
        String email = request.get("email");

        if (email == null || email.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("success", false, "message", "Email address cannot be empty"));
        }

        logger.info("收到重新发送验证邮件请求: {}", email);

        Map<String, Object> result = emailVerificationService.resendVerificationEmail(email.trim());

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 检查邮箱验证状态
     */
    @GetMapping("/status")
    public ResponseEntity<?> checkVerificationStatus(@RequestParam String email) {
        try {
            boolean isVerified = emailVerificationService.isEmailVerified(email);
            return ResponseEntity.ok(Map.of(
                "email", email,
                "verified", isVerified
            ));
        } catch (Exception e) {
            logger.error("检查邮箱验证状态失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "检查验证状态失败"));
        }
    }
}
