package com.tcmp2.controller;

import com.tcmp2.pojo.dto.ChangePasswordRequest;
import com.tcmp2.pojo.dto.ForgotPasswordRequest;
import com.tcmp2.pojo.dto.ResetPasswordRequest;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.PasswordResetService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 权限控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 12:00
 * @description : 处理用户权限相关的API请求
 */
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Resource
    private AuthorizationService authorizationService;

    @Resource
    private JwtUtil jwtUtil;

    @Resource
    private PasswordResetService passwordResetService;
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/user-info")
    public ResponseEntity<?> getCurrentUserInfo(HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        String currentRole = jwtUtil.extractRoleFromRequest(request);
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "User not authenticated"));
        }
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("username", currentUser);
        userInfo.put("role", currentRole);
        userInfo.put("isAdmin", authorizationService.isAdmin(currentUser));
        
        return ResponseEntity.ok(userInfo);
    }
    
    /**
     * 检查用户对特定材料的权限
     */
    @GetMapping("/material-permission/{materialId}")
    public ResponseEntity<?> checkMaterialPermission(@PathVariable String materialId, HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);

        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "User not authenticated"));
        }

        try {
            // 这里需要先获取材料信息，然后检查权限
            // 为了简化，我们先返回基本的权限信息
            Map<String, Object> permissions = new HashMap<>();
            permissions.put("canView", true); // 所有用户都可以查看
            permissions.put("isAdmin", authorizationService.isAdmin(currentUser));
            permissions.put("username", currentUser);

            return ResponseEntity.ok(permissions);
        } catch (Exception e) {
            logger.error("Error checking material permission", e);
            return ResponseEntity.status(500).body(Map.of("error", "Internal server error"));
        }
    }

    /**
     * 发送密码重置邮件
     */
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@RequestBody ForgotPasswordRequest request) {
        try {
            if (request.getUsernameOrEmail() == null || request.getUsernameOrEmail().trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("success", false, "message", "Username or email cannot be empty"));
            }

            Map<String, Object> result = passwordResetService.sendPasswordResetEmail(request.getUsernameOrEmail().trim());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error in forgot password", e);
            return ResponseEntity.ok(Map.of("success", false, "message", "System error, please try again later"));
        }
    }

    /**
     * 验证重置令牌
     */
    @GetMapping("/validate-reset-token")
    public ResponseEntity<?> validateResetToken(@RequestParam String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("valid", false, "message", "令牌不能为空"));
            }

            Map<String, Object> result = passwordResetService.validateResetToken(token.trim());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error validating reset token", e);
            return ResponseEntity.ok(Map.of("valid", false, "message", "系统错误"));
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody ResetPasswordRequest request) {
        try {
            if (request.getToken() == null || request.getToken().trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("success", false, "message", "Token cannot be empty"));
            }

            if (request.getNewPassword() == null || request.getNewPassword().trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("success", false, "message", "New password cannot be empty"));
            }

            Map<String, Object> result = passwordResetService.resetPassword(
                request.getToken().trim(),
                request.getNewPassword().trim()
            );
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error resetting password", e);
            return ResponseEntity.ok(Map.of("success", false, "message", "System error, please try again later"));
        }
    }

    /**
     * 修改密码（已登录用户）
     */
    @PostMapping("/change-password")
    public ResponseEntity<?> changePassword(@RequestBody ChangePasswordRequest request, HttpServletRequest httpRequest) {
        try {
            String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);

            if (currentUser == null) {
                return ResponseEntity.status(401).body(Map.of("success", false, "message", "User not logged in"));
            }

            if (request.getCurrentPassword() == null || request.getCurrentPassword().trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("success", false, "message", "Current password cannot be empty"));
            }

            if (request.getNewPassword() == null || request.getNewPassword().trim().isEmpty()) {
                return ResponseEntity.ok(Map.of("success", false, "message", "New password cannot be empty"));
            }

            Map<String, Object> result = passwordResetService.changePassword(
                currentUser,
                request.getCurrentPassword().trim(),
                request.getNewPassword().trim()
            );
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error changing password", e);
            return ResponseEntity.ok(Map.of("success", false, "message", "System error, please try again later"));
        }
    }
}
