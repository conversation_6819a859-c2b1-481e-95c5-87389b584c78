package com.tcmp2.controller;

import com.tcmp2.exception.BusinessException;
import com.tcmp2.service.AuthorizationService;
import com.tcmp2.service.DataCURDService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-02-19 00:00
 * @description : 材料数据CRUD控制器
 */
@RestController
@Validated
public class DataCURDController {

    private static final Logger logger = LoggerFactory.getLogger(DataCURDController.class);

    @Resource
    private DataCURDService dataCURDService;

    @Resource
    private JwtUtil jwtUtil;

    @Resource
    private AuthorizationService authorizationService;

    @PostMapping
    public ResponseEntity<?> addMaterial(@RequestBody Map<String, Object> data, HttpServletRequest request) {
        logger.info("Received request to add material: {}", data);

        if (data == null || data.isEmpty()) {
            throw new BusinessException("未提供材料数据", HttpStatus.BAD_REQUEST);
        }

        // 从JWT令牌中提取当前用户
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        if (currentUser == null) {
            throw new BusinessException("用户认证失败", HttpStatus.UNAUTHORIZED);
        }

        // 检查用户是否可以添加材料（游客不能添加）
        if (!authorizationService.canAddMaterial(currentUser)) {
            throw new BusinessException("权限不足，游客用户不能添加材料", HttpStatus.FORBIDDEN);
        }

        // 检查用户状态是否有效
        if (!authorizationService.isUserActive(currentUser)) {
            throw new BusinessException("用户账户已被禁用", HttpStatus.FORBIDDEN);
        }

        Map<String, Object> result = dataCURDService.addMaterial(data, currentUser);
        
        if (result.containsKey("error")) {
            String error = (String) result.get("error");

            if ("Material with this ID already exists".equals(error)) {
                throw new BusinessException("该ID的材料已存在", HttpStatus.CONFLICT);
            }

            throw new BusinessException(error);
        }

        return ResponseEntity.status(201).body(result);
    }

    @PutMapping("/{materialId}")
    public ResponseEntity<?> updateMaterial(
            @PathVariable @NotBlank(message = "材料ID不能为空") @Size(max = 100, message = "材料ID长度不能超过100个字符") String materialId,
            @RequestBody Map<String, Object> data,
            HttpServletRequest request) {
        logger.info("Received request to update material with ID: {}", materialId);

        if (data == null || data.isEmpty()) {
            throw new BusinessException("未提供更新数据", HttpStatus.BAD_REQUEST);
        }

        // 从JWT令牌中提取当前用户
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        if (currentUser == null) {
            throw new BusinessException("用户认证失败", HttpStatus.UNAUTHORIZED);
        }

        // 检查用户状态是否有效
        if (!authorizationService.isUserActive(currentUser)) {
            throw new BusinessException("用户账户已被禁用", HttpStatus.FORBIDDEN);
        }

        Map<String, Object> result = dataCURDService.updateMaterial(materialId, data, currentUser);
        
        if (result.containsKey("error")) {
            String error = (String) result.get("error");
            logger.warn("Error updating material: {}", error);
            
            if ("Material not found".equals(error)) {
                throw new BusinessException("未找到指定ID的材料", HttpStatus.NOT_FOUND);
            }
            
            throw new BusinessException(error);
        }
        
        logger.info("Successfully updated material");
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/{materialId}")
    public ResponseEntity<?> deleteMaterial(
            @PathVariable @NotBlank(message = "材料ID不能为空") @Size(max = 100, message = "材料ID长度不能超过100个字符") String materialId,
            HttpServletRequest request) {
        logger.info("Received request to delete material with ID: {}", materialId);

        // 从JWT令牌中提取当前用户
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        if (currentUser == null) {
            throw new BusinessException("用户认证失败", HttpStatus.UNAUTHORIZED);
        }

        // 检查用户状态是否有效
        if (!authorizationService.isUserActive(currentUser)) {
            throw new BusinessException("用户账户已被禁用", HttpStatus.FORBIDDEN);
        }

        Map<String, Object> result = dataCURDService.deleteMaterial(materialId, currentUser);
        
        if (result.containsKey("error") && "Material not found".equals(result.get("error"))) {
            logger.warn("Material not found for deletion");
            throw new BusinessException("未找到指定ID的材料", HttpStatus.NOT_FOUND);
        }
        
        logger.info("Successfully deleted material");
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/batch")
    public ResponseEntity<?> deleteAllMaterials(HttpServletRequest request) {
        try {
            logger.info("Received request to delete all materials");

            // 从JWT令牌中提取当前用户
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401).body("User not authenticated");
            }

            // 检查用户状态是否有效
            if (!authorizationService.isUserActive(currentUser)) {
                return ResponseEntity.status(403).body("用户账户已被禁用");
            }

            Map<String, Object> result = dataCURDService.deleteAllMaterials(currentUser);

            if (result.containsKey("error")) {
                String error = (String) result.get("error");
                logger.warn("Error deleting all materials: {}", error);

                if ("Only administrators can delete all materials".equals(error)) {
                    return ResponseEntity.status(403).body("只有管理员可以删除所有材料");
                }

                return ResponseEntity.status(500).body("删除所有材料时发生错误: " + error);
            }

            logger.info("Successfully deleted all materials");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("删除全部材料时发生异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }
}
