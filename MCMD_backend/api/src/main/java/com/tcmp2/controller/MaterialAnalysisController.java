package com.tcmp2.controller;

import com.tcmp2.service.DataCURDService;
import com.tcmp2.service.MaterialAnalysisService;
import com.tcmp2.utils.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 材料AI分析控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025/5/31
 * @description : 提供材料AI分析的REST API
 */
@RestController
@RequestMapping("/materials")
public class MaterialAnalysisController {

    private static final Logger logger = LoggerFactory.getLogger(MaterialAnalysisController.class);

    @Autowired
    private MaterialAnalysisService materialAnalysisService;

    @Autowired
    private DataCURDService dataCURDService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 创建SSE连接令牌（更安全的方式）
     * 前端先调用此接口获取短期令牌，然后用于SSE连接
     */
    @PostMapping("/{materialId}/analyze/token")
    public ResponseEntity<Map<String, String>> createAnalysisToken(@PathVariable String materialId, HttpServletRequest request) {
        try {
            // 验证用户身份
            String username = jwtUtil.extractUsernameFromRequest(request);
            if (username == null) {
                return ResponseEntity.status(401).body(Map.of("error", "未授权访问"));
            }

            // 验证材料是否存在
            Map<String, Object> materialData = dataCURDService.getMaterialById(materialId);
            if (materialData == null) {
                return ResponseEntity.status(404).body(Map.of("error", "材料不存在"));
            }

            // 生成短期有效的分析令牌（5分钟有效期）
            String analysisToken = jwtUtil.generateShortTermToken(username, materialId);

            return ResponseEntity.ok(Map.of(
                "token", analysisToken,
                "expiresIn", "300", // 5分钟
                "materialId", materialId
            ));
        } catch (Exception e) {
            logger.error("创建分析令牌失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "创建令牌失败"));
        }
    }

    /**
     * 分析指定材料 - 参考demo项目的ai3接口实现
     */
    @GetMapping(value = "/{materialId}/analyze", produces = "text/event-stream;charset=UTF-8")
    public SseEmitter analyzeMaterial(@PathVariable String materialId, HttpServletRequest request) {


        // 验证用户身份
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        if (currentUser == null) {
            logger.error("用户未认证 - 无法从请求中提取用户信息");
            SseEmitter emitter = new SseEmitter(60_000L);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("用户未认证，请先登录"));
                emitter.complete();
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }



        SseEmitter emitter = new SseEmitter(60_000L); // 60秒超时，与demo保持一致

        // 使用专用线程池处理异步流，参考demo的实现
        ExecutorService executor = Executors.newSingleThreadExecutor();

        // 设置emitter的超时和完成回调，确保资源清理
        emitter.onTimeout(() -> {
            logger.warn("SSE连接超时，materialId: {}", materialId);
            shutdownExecutorSafely(executor);
        });

        emitter.onCompletion(() -> {
            logger.debug("SSE连接完成，materialId: {}", materialId);
            shutdownExecutorSafely(executor);
        });

        emitter.onError((throwable) -> {
            logger.warn("SSE连接错误，materialId: {}, error: {}", materialId, throwable.getMessage());
            shutdownExecutorSafely(executor);
        });

        executor.execute(() -> {
            try {
                // 获取材料数据
                Map<String, Object> materialData = dataCURDService.getMaterialById(materialId);
                if (materialData == null) {
                    emitter.send(SseEmitter.event().data("材料不存在: " + materialId));
                    emitter.complete();
                    return;
                }

                // 获取CIF文件内容
                String cifContent = materialAnalysisService.getCifContent(materialId);

                // 获取AI分析流
                Flux<String> responseStream = materialAnalysisService.analyzeMaterial(materialData, cifContent);

                // 订阅流并发送数据，完全参考demo的ai3实现
                responseStream.subscribe(
                        content -> {
                            try {
                                // 检查emitter是否还可用
                                if (!isEmitterCompleted(emitter)) {
                                    emitter.send(SseEmitter.event().data(content));
                                }
                            } catch (IOException e) {
                                // 静默处理IO异常（通常是用户中断连接）
                                handleEmitterError(emitter, e);
                            } catch (IllegalStateException e) {
                                // 静默处理状态异常（emitter已完成）
                                handleEmitterError(emitter, e);
                            }
                        },
                        error -> {
                            // 错误处理 - 只记录非用户中断的错误
                            if (!isUserInterruptionError(error)) {
                                logger.error("AI分析流发生错误: {}", error.getMessage(), error);
                            }
                            handleEmitterError(emitter, error);
                        },
                        () -> {
                            // 完成处理
                            try {
                                if (!isEmitterCompleted(emitter)) {
                                    emitter.complete();
                                }
                            } catch (Exception e) {
                                // 静默处理完成时的异常
                            }
                        }
                );
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 处理analyze接口的OPTIONS预检请求
     */
    @RequestMapping(value = "/{materialId}/analyze", method = RequestMethod.OPTIONS)
    public ResponseEntity<?> handleAnalyzeOptions(@PathVariable String materialId) {
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "http://localhost:8080")
                .header("Access-Control-Allow-Credentials", "true")
                .header("Access-Control-Allow-Methods", "GET, OPTIONS")
                .header("Access-Control-Allow-Headers", "*")
                .header("Access-Control-Expose-Headers", "Cache-Control, Content-Type, Connection")
                .build();
    }

    /**
     * 获取材料基本信息（用于前端显示）
     */
    @GetMapping("/{materialId}/info")
    public ResponseEntity<?> getMaterialInfo(@PathVariable String materialId, HttpServletRequest request) {
        try {
            // 验证用户身份
            String currentUser = jwtUtil.extractUsernameFromRequest(request);
            if (currentUser == null) {
                return ResponseEntity.status(401).body("用户未认证");
            }

            // 获取材料数据
            Map<String, Object> materialData = dataCURDService.getMaterialById(materialId);
            if (materialData == null) {
                return ResponseEntity.status(404).body("材料不存在: " + materialId);
            }

            return ResponseEntity.ok(materialData);

        } catch (Exception e) {
            logger.error("获取材料信息时发生错误", e);
            return ResponseEntity.status(500).body("获取材料信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查emitter是否已完成
     */
    private boolean isEmitterCompleted(SseEmitter emitter) {
        try {
            // 通过反射检查emitter的内部状态
            java.lang.reflect.Field field = emitter.getClass().getDeclaredField("complete");
            field.setAccessible(true);
            return (Boolean) field.get(emitter);
        } catch (Exception e) {
            // 如果无法检查状态，假设未完成
            return false;
        }
    }

    /**
     * 安全地处理emitter错误
     */
    private void handleEmitterError(SseEmitter emitter, Throwable error) {
        try {
            if (!isEmitterCompleted(emitter)) {
                emitter.completeWithError(error);
            }
        } catch (Exception e) {
            // 静默处理 - emitter可能已经完成
        }
    }

    /**
     * 判断是否为用户中断错误
     */
    private boolean isUserInterruptionError(Throwable error) {
        if (error == null) return false;

        String message = error.getMessage();
        if (message == null) return false;

        // 检查常见的用户中断错误模式
        return message.contains("Broken pipe") ||
               message.contains("Connection reset") ||
               message.contains("AsyncRequestNotUsableException") ||
               message.contains("ResponseBodyEmitter has already completed") ||
               error instanceof java.io.IOException && message.contains("flush");
    }

    /**
     * 安全地关闭ExecutorService
     */
    private void shutdownExecutorSafely(ExecutorService executor) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            executor.shutdown();
            // 等待正在执行的任务完成，最多等待5秒
            if (!executor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                logger.warn("ExecutorService未能在5秒内正常关闭，强制关闭");
                executor.shutdownNow();
                // 再等待2秒确保强制关闭完成
                if (!executor.awaitTermination(2, java.util.concurrent.TimeUnit.SECONDS)) {
                    logger.error("ExecutorService强制关闭失败");
                }
            }
        } catch (InterruptedException e) {
            logger.warn("等待ExecutorService关闭时被中断");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("关闭ExecutorService时发生异常", e);
        }
    }
}
