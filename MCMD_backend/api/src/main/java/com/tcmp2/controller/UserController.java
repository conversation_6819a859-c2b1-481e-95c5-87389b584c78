package com.tcmp2.controller;

import com.tcmp2.pojo.entity.User;
import com.tcmp2.pojo.enums.UserRole;
import com.tcmp2.service.UserService;
import com.tcmp2.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 *
 * <AUTHOR> <PERSON>
 * @createDate : 2025-06-25 10:00
 * @description : 处理用户注册、管理相关的API请求
 */
@RestController
@RequestMapping("/user")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Resource
    private UserService userService;
    
    @Resource
    private JwtUtil jwtUtil;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody Map<String, String> registerRequest) {
        String username = registerRequest.get("username");
        String password = registerRequest.get("password");
        String email = registerRequest.get("email");
        String realName = registerRequest.get("realName");
        String organization = registerRequest.get("organization");
        
        logger.info("收到用户注册请求: {}", username);

        Map<String, Object> result = userService.register(username, password, email, realName, organization);
        
        if (result.containsKey("error")) {
            return ResponseEntity.badRequest().body(result);
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取所有用户列表（仅管理员）
     */
    @GetMapping("/list")
    public ResponseEntity<?> getAllUsers(HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        try {
            List<User> users = userService.getAllUsers(currentUser);
            return ResponseEntity.ok(Map.of("users", users));
        } catch (SecurityException e) {
            return ResponseEntity.status(403).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "获取用户列表失败"));
        }
    }
    
    /**
     * 更新用户角色（仅管理员）
     */
    @PutMapping("/{username}/role")
    public ResponseEntity<?> updateUserRole(
            @PathVariable String username,
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);
        String newRoleStr = request.get("role");
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        if (newRoleStr == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "角色参数不能为空"));
        }
        
        try {
            UserRole newRole = UserRole.fromCode(newRoleStr);
            Map<String, String> result = userService.updateUserRole(currentUser, username, newRole);
            
            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户角色失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "更新用户角色失败"));
        }
    }
    
    /**
     * 更新用户状态（仅管理员）
     */
    @PutMapping("/{username}/status")
    public ResponseEntity<?> updateUserStatus(
            @PathVariable String username,
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        String currentUser = jwtUtil.extractUsernameFromRequest(httpRequest);
        String newStatus = request.get("status");
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        if (newStatus == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "状态参数不能为空"));
        }
        
        try {
            Map<String, String> result = userService.updateUserStatus(currentUser, username, newStatus);
            
            if (result.containsKey("error")) {
                return ResponseEntity.badRequest().body(result);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "更新用户状态失败"));
        }
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public ResponseEntity<?> getUserInfo(HttpServletRequest request) {
        String currentUser = jwtUtil.extractUsernameFromRequest(request);
        
        if (currentUser == null) {
            return ResponseEntity.status(401).body(Map.of("error", "用户未认证"));
        }
        
        try {
            User user = userService.getUserInfo(currentUser);
            if (user == null) {
                return ResponseEntity.status(404).body(Map.of("error", "User not found"));
            }
            
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "获取用户信息失败"));
        }
    }
    
    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public ResponseEntity<?> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.isUsernameExists(username);
            Map<String, Object> response = new HashMap<>();
            response.put("exists", exists);
            response.put("available", !exists);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("检查用户名失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "检查用户名失败"));
        }
    }
    
    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public ResponseEntity<?> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.isEmailExists(email);
            Map<String, Object> response = new HashMap<>();
            response.put("exists", exists);
            response.put("available", !exists);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("检查邮箱失败", e);
            return ResponseEntity.status(500).body(Map.of("error", "检查邮箱失败"));
        }
    }
}
