package com.tcmp2.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @createDate : 2025-01-20 15:00
 * @description : 统一错误处理工具类
 * 提供标准化的错误消息处理和用户友好的错误信息
 */
public class ErrorMessageUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ErrorMessageUtil.class);
    
    // 错误类型常量
    public static final String ERROR_TYPE_VALIDATION = "VALIDATION_ERROR";
    public static final String ERROR_TYPE_AUTHENTICATION = "AUTHENTICATION_ERROR";
    public static final String ERROR_TYPE_AUTHORIZATION = "AUTHORIZATION_ERROR";
    public static final String ERROR_TYPE_NOT_FOUND = "NOT_FOUND_ERROR";
    public static final String ERROR_TYPE_DUPLICATE = "DUPLICATE_ERROR";
    public static final String ERROR_TYPE_FILE_OPERATION = "FILE_OPERATION_ERROR";
    public static final String ERROR_TYPE_DATABASE = "DATABASE_ERROR";
    public static final String ERROR_TYPE_EXTERNAL_SERVICE = "EXTERNAL_SERVICE_ERROR";
    public static final String ERROR_TYPE_UNKNOWN = "UNKNOWN_ERROR";
    
    // 用户友好的错误消息映射
    private static final Map<String, String> USER_FRIENDLY_MESSAGES = new HashMap<>();
    
    static {
        // 验证错误
        USER_FRIENDLY_MESSAGES.put("VALIDATION_ERROR", "Input data format is incorrect, please check and try again");
        USER_FRIENDLY_MESSAGES.put("MISSING_REQUIRED_FIELD", "Missing required fields, please complete the information and try again");
        USER_FRIENDLY_MESSAGES.put("INVALID_FORMAT", "Data format is incorrect, please check the input content");

        // 认证和授权错误
        USER_FRIENDLY_MESSAGES.put("AUTHENTICATION_ERROR", "Authentication failed, please login again");
        USER_FRIENDLY_MESSAGES.put("AUTHORIZATION_ERROR", "You do not have permission to perform this operation");
        USER_FRIENDLY_MESSAGES.put("SESSION_EXPIRED", "Login session has expired, please login again");

        // 资源错误
        USER_FRIENDLY_MESSAGES.put("NOT_FOUND_ERROR", "The requested resource does not exist");
        USER_FRIENDLY_MESSAGES.put("DUPLICATE_ERROR", "Data already exists, please check and try again");

        // 文件操作错误
        USER_FRIENDLY_MESSAGES.put("FILE_UPLOAD_ERROR", "File upload failed, please try again");
        USER_FRIENDLY_MESSAGES.put("FILE_NOT_FOUND", "File does not exist or has been deleted");
        USER_FRIENDLY_MESSAGES.put("FILE_FORMAT_ERROR", "File format is not supported");

        // 数据库错误
        USER_FRIENDLY_MESSAGES.put("DATABASE_ERROR", "Database operation failed, please try again later");
        USER_FRIENDLY_MESSAGES.put("CONNECTION_ERROR", "Connection failed, please check your network and try again");

        // 外部服务错误
        USER_FRIENDLY_MESSAGES.put("EXTERNAL_SERVICE_ERROR", "External service is temporarily unavailable, please try again later");

        // 通用错误
        USER_FRIENDLY_MESSAGES.put("UNKNOWN_ERROR", "System is busy, please try again later");
    }
    
    /**
     * 根据异常获取错误类型
     */
    public static String getErrorType(Exception error) {
        if (error == null) {
            return ERROR_TYPE_UNKNOWN;
        }
        
        String message = error.getMessage();
        if (message == null) {
            return ERROR_TYPE_UNKNOWN;
        }
        
        // 根据异常类型和消息内容判断错误类型
        if (error instanceof IllegalArgumentException || 
            message.contains("validation") || 
            message.contains("invalid")) {
            return ERROR_TYPE_VALIDATION;
        }
        
        if (message.contains("authentication") || 
            message.contains("login") || 
            message.contains("credential")) {
            return ERROR_TYPE_AUTHENTICATION;
        }
        
        if (message.contains("authorization") || 
            message.contains("permission") || 
            message.contains("access denied")) {
            return ERROR_TYPE_AUTHORIZATION;
        }
        
        if (message.contains("not found") || 
            message.contains("does not exist")) {
            return ERROR_TYPE_NOT_FOUND;
        }
        
        if (message.contains("duplicate") || 
            message.contains("already exists") || 
            message.contains("ID重复")) {
            return ERROR_TYPE_DUPLICATE;
        }
        
        if (message.contains("file") || 
            message.contains("upload") || 
            message.contains("CIF")) {
            return ERROR_TYPE_FILE_OPERATION;
        }
        
        if (message.contains("database") || 
            message.contains("connection") || 
            message.contains("MongoDB")) {
            return ERROR_TYPE_DATABASE;
        }
        
        return ERROR_TYPE_UNKNOWN;
    }
    
    /**
     * 获取用户友好的错误消息
     */
    public static String getUserFriendlyMessage(String errorType) {
        return USER_FRIENDLY_MESSAGES.getOrDefault(errorType, 
            USER_FRIENDLY_MESSAGES.get(ERROR_TYPE_UNKNOWN));
    }
    
    /**
     * 获取用户友好的错误消息（基于异常）
     */
    public static String getUserFriendlyMessage(Exception error) {
        String errorType = getErrorType(error);
        return getUserFriendlyMessage(errorType);
    }
    
    /**
     * 创建标准化的错误响应
     */
    public static Map<String, Object> createErrorResponse(Exception error) {
        String errorType = getErrorType(error);
        String userMessage = getUserFriendlyMessage(errorType);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorType", errorType);
        response.put("message", userMessage);
        
        // 记录详细错误信息到日志
        logger.error("Error occurred - Type: {}, Message: {}", errorType, error.getMessage(), error);
        
        return response;
    }
    
    /**
     * 创建标准化的错误响应（自定义消息）
     */
    public static Map<String, Object> createErrorResponse(String errorType, String customMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorType", errorType);
        response.put("message", customMessage != null ? customMessage : getUserFriendlyMessage(errorType));
        
        return response;
    }
    
    /**
     * 安全地获取异常消息（不暴露敏感信息）
     */
    public static String getSafeErrorMessage(Exception error) {
        if (error == null) {
            return "Unknown error occurred";
        }
        
        // 对于某些敏感异常，不直接暴露原始消息
        if (error instanceof SecurityException || 
            error.getClass().getName().contains("Security") ||
            error.getClass().getName().contains("Auth")) {
            return getUserFriendlyMessage(ERROR_TYPE_AUTHENTICATION);
        }
        
        return getUserFriendlyMessage(error);
    }
    
    /**
     * 判断是否为用户操作错误（非系统错误）
     */
    public static boolean isUserError(Exception error) {
        String errorType = getErrorType(error);
        return ERROR_TYPE_VALIDATION.equals(errorType) || 
               ERROR_TYPE_AUTHENTICATION.equals(errorType) || 
               ERROR_TYPE_AUTHORIZATION.equals(errorType) || 
               ERROR_TYPE_NOT_FOUND.equals(errorType) || 
               ERROR_TYPE_DUPLICATE.equals(errorType);
    }
}
